import 'dart:async';
import 'dart:ui';

import 'package:audioplayers/audioplayers.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/bai_models/favourite_item.dart';
import 'package:connectone/bai_screens/history_offers_more.dart';
import 'package:connectone/bai_screens/mr_grouping.dart';
import 'package:connectone/bai_screens/edit_assign_approve.dart';
import 'package:connectone/bai_screens/project_details.dart';
import 'package:connectone/core/bai_widgets/bai_image.dart';
import 'package:connectone/core/bai_widgets/help_info.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/old_models/favourites_response.dart';
import 'package:connectone/old_models/firebase_response.dart';
import 'package:connectone/core/utils/firebase_manager.dart';
import 'package:connectone/core/utils/offline_bid_manager.dart';
import 'package:connectone/core/utils/offline_utils.dart';
import 'package:connectone/core/utils/time_utils.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_models/user_action_body.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
// import 'package:flutter_barcode_scanner/flutter_barcode_scanner.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:rotated_corner_decoration/rotated_corner_decoration.dart';
import 'package:share_plus/share_plus.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';
import 'package:url_launcher/url_launcher.dart';
import '../bai_cart/bai_cart.dart';
import '../core/bai_widgets/user_profile_dialog.dart';
import '../core/bai_widgets/split_group_name_dialog.dart';
import '../old_blocs/offline_card/offline_card_bloc.dart';
import '../old_blocs/offline_stocks/offline_stocks_bloc.dart';
import '../core/old_widgets/common/active_buyer_count.dart';
import '../core/old_widgets/common/card_list_item.dart';
import '../core/utils/data_storage.dart';
import '../core/utils/circular_progress.dart';
import '../core/utils/colors.dart';
import '../core/utils/constants.dart';

//ignore: must_be_immutable
class OfflineScreen extends StatefulWidget {
  OfflineScreen({
    Key? key,
    required this.query,
    required this.title,
    required this.userType,
    required this.categoryType,
    required this.isAdmin,
  }) : super(key: key);

  String query;
  String title;
  String userType;
  String categoryType;
  bool isAdmin;

  @override
  State<OfflineScreen> createState() => _OfflineScreenState();
}

class _OfflineScreenState extends State<OfflineScreen> {
  bool isLoading = false;
  bool showOnlyFavourites = false;
  String? _selectedValue;
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  var offlineStocks = BaiProductsRes();
  final ScrollController _scrollController = ScrollController();

  late String query;
  late String title;
  late String userType;
  late String categoryType;
  late bool isAdmin;

  @override
  void initState() {
    extractArgs();
    super.initState();
    fetchStocks();
    scrollListener();
    createTutorial();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Welcome to the Purchase Orders/Enquiries screen!\n\nHere you can view and manage all your purchase orders and enquiries.\n\nKey Features:\n• Product Details: View item name, specifications, quantity and category\n• Order Info: Check creation date, delivery date and location details\n• Attachments: Access photos, documents and voice notes\n• Status Updates: Track order progress from creation to completion\n• Actions: Submit offers, mark favorites, share listings\n• Filters: Sort by created date, delivery date or quantity\n• Search: Find specific orders using ID search\n\nUse the Open/Close button to expand order details.\nTap the heart icon to favorite items for quick access.\n\nTap anywhere to exit the tutorial.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  void extractArgs() {
    final Map<String, dynamic> args = Get.arguments as Map<String, dynamic>;
    query = args['query'] ?? '';
    title = args['title'] ?? 'Default Title';
    userType = args['userType'] ?? 'guest';
    categoryType = args['categoryType'] ?? '';
    isAdmin = args['isAdmin'] ?? false;
    setState(() {
      widget.query = query;
      widget.title = title;
      widget.userType = userType;
      widget.categoryType = categoryType;
      widget.isAdmin = getRoleLevel() == 1;
    });
  }

  int getMode() {
    if (widget.categoryType == "OPOE" ||
        widget.categoryType == "CPOE" ||
        widget.categoryType == "NPOE" ||
        widget.categoryType == "EPOE") {
      return 1;
    } else if (widget.categoryType == "OPOR" ||
        widget.categoryType == "CPOR" ||
        widget.categoryType == "EPOR") {
      return 2;
    } else if (widget.categoryType == "ANPE" || widget.categoryType == "TMBR") {
      return 3;
    } else {
      return 1;
    }
  }

  scrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        setState(() {
          isLoading = true;
        });
        fetchNextStocks();
      }
      if (_scrollController.position.userScrollDirection ==
          ScrollDirection.reverse) {
        // setState(() {
        //   showMenu = true;
        // });
      } else if (_scrollController.position.userScrollDirection ==
          ScrollDirection.forward) {
        // setState(() {
        //   showMenu = false;
        // });
      }
    });
  }

  bool showMenu = false;

  void fetchStocks() {
    context.read<OfflineMainBloc>().add(
          InitializeOfflineStocks(
            offlineStocks: offlineStocks,
            queryString: widget.query,
            code: widget.categoryType,
          ),
        );
  }

  void fetchNextStocks() {
    context.read<OfflineMainBloc>().add(
          InitializeOfflineStocksNext(
            offlineStocks: offlineStocks,
            queryString: widget.query,
            code: widget.categoryType,
          ),
        );
  }

  /// Shows the split group dialog for the specific item ID
  Future<void> _showSplitGroupDialog(
    BuildContext context,
    int prchOrdrId,
    String categoryName,
  ) async {
    final splitGroupResult = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (BuildContext context) {
        return SplitGroupNameDialog(
          fixedPrefix: "$categoryName ",
          prchOrdrId: prchOrdrId,
        );
      },
    );

    // If user confirmed the split, proceed to ProjectDetails
    if (splitGroupResult != null && splitGroupResult['id'] != null) {
      var content = BaiCart.getContent();
      if (content == null) {
        return;
      }
      var orderGroupId = content.orderGroupId;
      var category = content.cappCategoriesName;
      var categoryId = content.cappCategoriesId;
      Get.to(
        ProjectDetails(
          orderGroupId: orderGroupId?.toInt(),
          deliveryDate: splitGroupResult['date'],
          category: category,
          isMr: content.isMr ?? true,
          splitSiteId: content.projectId?.toInt(),
          splitGroupId: splitGroupResult['id'],
          categoryId: categoryId?.toInt(),
          splitGroupName: splitGroupResult['name'],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: scaffoldKey,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.primaryColor,
        title: Text(widget.title),
        actions: [
          InfoHelp(
            key: key1,
            onTap: () {
              showTutorial();
            },
          ),
        ],
      ),
      body: BlocConsumer<OfflineMainBloc, OfflineMainState>(
        listener: (context, state) {
          if (state is OfflineStocksLoaded) {
            setState(() {
              isLoading = false;
            });
          }
          if (state is OfflineStocksRefresh) {
            context.read<OfflineMainBloc>().add(
                  InitializeOfflineStocks(
                    offlineStocks: offlineStocks,
                    queryString: widget.query,
                    code: widget.categoryType,
                  ),
                );
          }
          if (state is ShowSplitGroupDialogState) {
            // Show the split group dialog for the specific item ID
            _showSplitGroupDialog(
              context,
              state.itemId,
              state.categoryName,
            );
          }
        },
        builder: (context, state1) {
          if (state1 is OfflineStocksLoaded) {
            return SizedBox(
              height: MediaQuery.of(context).size.height - 120,
              child: RefreshIndicator(
                onRefresh: () {
                  context.read<OfflineMainBloc>().add(
                        InitializeOfflineStocks(
                          offlineStocks: offlineStocks,
                          queryString: widget.query,
                          code: widget.categoryType,
                        ),
                      );
                  return Future.value();
                },
                child: Column(
                  children: [
                    AnimatedSize(
                      duration: const Duration(milliseconds: 300),
                      child: !showMenu
                          ? Row(
                              children: [
                                const SizedBox(width: 12),
                                Expanded(
                                  flex: 2,
                                  child: SizedBox(
                                    height: 32,
                                    child: MaterialButton(
                                      padding: const EdgeInsets.all(4),
                                      minWidth: 0,
                                      onPressed: () {
                                        setState(() {
                                          showOnlyFavourites =
                                              !showOnlyFavourites;
                                        });
                                      },
                                      height: 24,
                                      shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(16)),
                                      color: showOnlyFavourites
                                          ? Colors.indigo
                                          : AppColors.primaryColor,
                                      textColor: AppColors.white,
                                      child: const Text(
                                        "Favourites",
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 4.0),
                                const Expanded(
                                  flex: 1,
                                  child: SizedBox(),
                                ),
                                if (widget.query
                                    .contains("orderGroupName=")) ...[
                                  Container(
                                    padding:
                                        const EdgeInsets.fromLTRB(16, 8, 12, 8),
                                    decoration: BoxDecoration(
                                      color: Colors.grey[200],
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          widget.query
                                              .split("orderGroupName=")[1],
                                          style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(width: 4),
                                        InkWell(
                                          onTap: () {
                                            setState(() {
                                              widget.query = widget.query
                                                  .split("&orderGroupName=")[0];
                                            });
                                            context.read<OfflineMainBloc>().add(
                                                  InitializeOfflineStocks(
                                                    offlineStocks:
                                                        offlineStocks,
                                                    sortBy: "NONE",
                                                    queryString: widget.query
                                                        .split(
                                                            "&orderGroupName=")[0],
                                                    code: widget.categoryType,
                                                  ),
                                                );
                                          },
                                          child:
                                              const Icon(Icons.close, size: 16),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                                if (!widget.query.contains("orderGroupName="))
                                  IconButton(
                                    onPressed: () async {
                                      var term = await showDialog(
                                          context: context,
                                          builder: (context) {
                                            return const SearchPopup();
                                          });
                                      if (term == null || term.isEmpty) return;
                                      setState(() {
                                        widget.query =
                                            "${widget.query}&orderGroupName=$term";
                                      });
                                      context.read<OfflineMainBloc>().add(
                                            InitializeOfflineStocks(
                                              offlineStocks: offlineStocks,
                                              sortBy: "NONE",
                                              queryString:
                                                  "${widget.query}&orderGroupName=$term",
                                              code: widget.categoryType,
                                            ),
                                          );
                                    },
                                    icon: const Icon(Icons.search),
                                  ),
                                const SizedBox(width: 4.0),
                                DropdownButton<String>(
                                  underline:
                                      Container(color: Colors.transparent),
                                  icon: const Icon(
                                    Icons.arrow_drop_down,
                                    color: Colors.black,
                                  ),
                                  alignment: Alignment.centerRight,
                                  hint: const Text(
                                    "Sort",
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black,
                                    ),
                                  ),
                                  style: const TextStyle(color: Colors.black),
                                  dropdownColor: Colors.white,
                                  items: <String>[
                                    "Created On",
                                    "Delivery On",
                                    "Quantity",
                                  ].map((String value) {
                                    return DropdownMenuItem<String>(
                                      value: value,
                                      child: Container(
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8.0),
                                          color: _selectedValue == value
                                              ? AppColors.primaryColor
                                              : Colors.white,
                                        ),
                                        padding: const EdgeInsets.all(8.0),
                                        child: Text(
                                          value,
                                          style: TextStyle(
                                            fontWeight: _selectedValue == value
                                                ? FontWeight.bold
                                                : FontWeight.normal,
                                            color: _selectedValue == value
                                                ? Colors.white
                                                : Colors.black,
                                          ),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    String newValue;
                                    if (_selectedValue == value) {
                                      newValue = "NONE";
                                    } else {
                                      newValue = value!;
                                    }

                                    setState(() {
                                      _selectedValue =
                                          newValue == "NONE" ? null : newValue;
                                    });

                                    context.read<OfflineMainBloc>().add(
                                          InitializeOfflineStocks(
                                            offlineStocks: offlineStocks,
                                            sortBy: newValue,
                                            queryString: widget.query,
                                            code: widget.categoryType,
                                          ),
                                        );
                                  },
                                  value: _selectedValue,
                                ),
                                const SizedBox(width: 4.0),
                              ],
                            )
                          : const SizedBox.shrink(),
                    ),
                    Expanded(
                      child: state1.offlineStocks.content!.isNotEmpty
                          ? ListView.builder(
                              scrollDirection: Axis.vertical,
                              physics: const ClampingScrollPhysics(),
                              shrinkWrap: true,
                              controller: _scrollController,
                              itemCount: state1.offlineStocks.content!.length,
                              itemBuilder: (BuildContext context3, int index) {
                                var currentItem =
                                    state1.offlineStocks.content![index];
                                if (OfflineUtils().isFavourite(
                                        state1.favouritesResponse,
                                        currentItem.prchOrdrId.toString()) &&
                                    showOnlyFavourites) {
                                  return BlocBuilder<OfflineMainBloc,
                                      OfflineMainState>(
                                    builder: (context, state) {
                                      return BlocProvider(
                                        create: (context3) => OfflineSubBloc(),
                                        child: OfflineScreenItem(
                                          currentItem: currentItem,
                                          favouritesResponse:
                                              state1.favouritesResponse,
                                          parentState: state1,
                                          query: widget.query,
                                          mode: getMode(),
                                          categoryType: widget.categoryType,
                                          isAdmin: widget.isAdmin,
                                        ),
                                      );
                                    },
                                  );
                                } else if (!showOnlyFavourites) {
                                  return BlocBuilder<OfflineMainBloc,
                                      OfflineMainState>(
                                    builder: (context, state) {
                                      return BlocProvider(
                                        create: (context3) => OfflineSubBloc(),
                                        child: OfflineScreenItem(
                                          currentItem: currentItem,
                                          favouritesResponse:
                                              state1.favouritesResponse,
                                          parentState: state1,
                                          query: widget.query,
                                          mode: getMode(),
                                          isAdmin: widget.isAdmin,
                                          categoryType: widget.categoryType,
                                        ),
                                      );
                                    },
                                  );
                                } else {
                                  return const SizedBox.shrink();
                                }
                              })
                          : Center(
                              child: Text((getMode() == 1 || getMode() == 3)
                                  ? "No Purchase Enquires"
                                  : "No Purchase Orders"),
                            ),
                    ),
                    isLoading
                        ? Container(
                            height: 48,
                            color: Colors.transparent,
                            child: Center(
                              child: progressIndicator,
                            ),
                          )
                        : const SizedBox(),
                  ],
                ),
              ),
            );
          } else {
            return Center(
              child: progressIndicator,
            );
          }
        },
      ),
    );
  }
}

class OfflineScreenItem extends StatefulWidget {
  const OfflineScreenItem({
    Key? key,
    required this.currentItem,
    required this.favouritesResponse,
    required this.parentState,
    required this.query,
    required this.mode,
    required this.categoryType,
    required this.isAdmin,
  }) : super(key: key);

  final Content currentItem;
  final List<FavouriteItem>? favouritesResponse;
  final String query;
  final OfflineStocksLoaded parentState;
  final int mode;
  final String categoryType;
  final bool isAdmin;

  @override
  State<OfflineScreenItem> createState() => _OfflineScreenItemState();
}

class _OfflineScreenItemState extends State<OfflineScreenItem>
    with AutomaticKeepAliveClientMixin {
  StreamSubscription? streamSubscription;

  var format = NumberFormat.simpleCurrency(locale: 'HI');

  var buttonText = "Open";
  var groupValue = "TABLE";
  bool isRead = false;

  var isOpen = false;
  var isTable = true;
  var showMargin = false;
  double margin = 0;
  var activeBuyerCount = 0;
  var showActiveBuyerCount = false;

  FirebaseResponseOffline firebaseResponse = FirebaseResponseOffline(
    stockAuctionStatus: "",
    bidUpdateTimestamp: "2023-01-01 14:30:00",
    stockStatusDescription: "",
    buyNowPrice: 0,
    highestBidCustomerId: 0,
    auctionEndTs: "2023-01-01 14:30:00",
    highestBid: 0,
  );

  void firebaseListener() {
    if (!(OfflineUtils().isFavourite(widget.favouritesResponse,
            widget.currentItem.prchOrdrId.toString()) ||
        isOpen)) {
      return;
    }
    if (streamSubscription != null) {
      return;
    }
    // print("object12332326473647836486387456");
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/offline_auction')
        .child(widget.currentItem.prchOrdrId.toString());
    streamSubscription = dbReference.onValue.listen(
      (DatabaseEvent event) {
        if (event.snapshot.exists) {
          var data = event.snapshot;
          firebaseResponse = FirebaseResponseOffline.fromSnapshot(data);
          setState(() {
            firebaseResponse = FirebaseResponseOffline.fromSnapshot(data);
            // print("object1 ${firebaseResponse.auctionEndTs}");
          });
          if (firebaseResponse.stockAuctionStatus != "BIDG") {
            context.read<OfflineMainBloc>().add(
                  InitializeOfflineStocks(
                    offlineStocks: BaiProductsRes(),
                    queryString: widget.query,
                    code: widget.categoryType,
                  ),
                );
          }
        } else {
          // todo
        }
      },
    );
  }

  @override
  void initState() {
    super.initState();
    // firebaseListener();
    context.read<OfflineSubBloc>().add(
          InitializeFb(
            buttonText,
            widget.currentItem.prchOrdrId.toString(),
            groupValue,
            isOpen,
            isTable,
            firebaseResponse,
          ),
        );
  }

  @override
  void dispose() {
    super.dispose();
    streamSubscription?.cancel();
  }

  bool isSelectedItem() {
    if (widget.parentState.selectedItem == widget.currentItem.prchOrdrId) {
      return true;
    } else {
      return false;
    }
  }

  List<String> urls = [];
  List<String> images = [];
  List<String> files = [];
  List<String> audios = [];

  bool playing = false;

  final player = AudioPlayer();

  void _setArrays() {
    final item = widget.currentItem;

    urls = [
      if (item.image1 != null) item.image1!,
      if (item.image2 != null) item.image2!,
      if (item.image3 != null) item.image3!,
      if (item.image4 != null) item.image4!,
      if (item.image5 != null) item.image5!,
    ];

    for (var url in urls) {
      if (_isImage(url)) {
        images.add(url);
      } else if (_isAudio(url)) {
        audios.add(url);
      } else {
        files.add(url);
      }
    }
  }

  static bool _isImage(String url) {
    return url.endsWith('.jpg') ||
        url.endsWith('.jpeg') ||
        url.endsWith('.png') ||
        url.endsWith('.gif') ||
        url.endsWith('.bmp');
  }

  static bool _isAudio(String url) {
    return url.endsWith('.mp3') ||
        url.endsWith('.wav') ||
        url.endsWith('.aac') ||
        url.endsWith('.ogg') ||
        url.endsWith('.mp4');
  }

  bool showQuantity() {
    return !(widget.currentItem.mvtItemName ?? "")
        .toLowerCase()
        .contains("reinforcement");
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    _setArrays();
    var mrStatus = widget.currentItem.statusCd ?? "";
    var enableSubmit = ["QUOT", "NEGO", "VASS"].contains(mrStatus);
    var dp = widget.currentItem.mvtItemImage;
    return BlocBuilder<OfflineSubBloc, OfflineSubState>(
      builder: (context, parentState) {
        if (parentState is OfflineCardLoaded) {
          ///
          var configData = DataStorage.configData?.firstWhereOrNull(
              (element) => element?.keyName1 == "margin_required_yn");
          if (configData?.valueName1 == "Y") {
            var marginPercentageData = DataStorage.configData?.firstWhereOrNull(
                (element) => element?.keyName1 == "margin_percentage");
            var percentage = marginPercentageData?.valueName1;
            var totalVolume = widget.currentItem.quantity;
            var currentBid = firebaseResponse.highestBid;

            var totalAmount = totalVolume! * currentBid;
            var marginValue = double.parse(percentage!) / 100 * totalAmount;

            margin = marginValue.toDouble();

            showMargin = true;
          } else {
            showMargin = false;
          }

          ///

          ///
          if (DataStorage.configData
                  ?.firstWhereOrNull(
                      (element) => element?.keyName1 == "active_buyer_info_yn")
                  ?.valueName1 ==
              "Y") {
            if (firebaseResponse.highestBidCustomerId != getCustomerId()) {
              showActiveBuyerCount = true;
            } else {
              showActiveBuyerCount = false;
            }
          }

          ///
          var isNego = widget.currentItem.buyerNegoYN == "Y" ||
              widget.currentItem.sellerNegoYN == "Y";
          return GestureDetector(
            onTap: () {
              context.read<OfflineMainBloc>().add(
                  ItemSelected(widget.currentItem.prchOrdrId?.toInt() ?? 0));
            },
            child: Container(
              foregroundDecoration: RotatedCornerDecoration.withColor(
                badgeSize: const Size(28, 28),
                badgeCornerRadius: const Radius.circular(8),
                badgePosition: BadgePosition.topEnd,
                color: getCategoryColor(widget.currentItem.statusCd ?? "") ??
                    Colors.transparent,
                textSpan: null,
              ),
              margin: const EdgeInsets.all(8.0),
              child: Card(
                // color: Highlight ? Colors.lightBlueAccent : Colors.lightGreenAccent,
                shape: RoundedRectangleBorder(
                  borderRadius: const BorderRadius.all(
                    Radius.circular(12.0),
                  ),
                  side: isSelectedItem()
                      ? const BorderSide(
                          color: AppColors.green,
                          width: 2,
                        )
                      : const BorderSide(
                          color: Colors.transparent,
                          width: 2,
                        ),
                ),
                elevation: 4.0,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(
                      Radius.circular(12.0),
                    ),
                    color: isNego
                        ? Colors.yellow
                        : (widget.currentItem.statusCd.toString() == "SPLT")
                            ? getCategoryColor(
                                widget.currentItem.statusCd.toString(),
                              )
                            : Colors.white,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(
                              height: 56,
                              width: 56,
                              child: Card(
                                shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(6),
                                  ),
                                ),
                                clipBehavior: Clip.hardEdge,
                                elevation: 2.0,
                                child: GestureDetector(
                                  onTap: () {
                                    if (dp != null) {
                                      showImageDialog(context, dp);
                                    }
                                  },
                                  child: Center(
                                    child: dp != null
                                        ? Image.network(
                                            dp,
                                            fit: BoxFit.fill,
                                          )
                                        : Image.asset(
                                            'assets/images/no_image_available.jpeg',
                                            fit: BoxFit.fill,
                                          ),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 4),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  width: kIsWeb
                                      ? 400
                                      : MediaQuery.of(context).size.width - 100,
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          widget.currentItem.mvtItemName
                                                  ?.toString() ??
                                              "N/A",
                                          style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16),
                                        ),
                                      ),
                                      const SizedBox(width: 6),
                                      const SizedBox(width: 16),
                                      (widget.currentItem.isRead == true ||
                                              isRead)
                                          ? const Icon(Icons.drafts_outlined)
                                          : const Icon(Icons.mail_outline),
                                      const SizedBox(width: 4),
                                      IconButton(
                                        icon: Icon(
                                          Icons.ios_share,
                                          color: AppColors.primaryColor,
                                        ),
                                        onPressed: () async {
                                          final productUrl =
                                              'https://baistore.cochq.au/mr?id=${widget.currentItem.prchOrdrId}';
                                          final message =
                                              'Discover this product (ID: ${widget.currentItem.orderGroupName}) at $productUrl on the bai Store App.';
                                          await Share.share(message);
                                        },
                                        padding:
                                            const EdgeInsets.only(bottom: 6),
                                        constraints: const BoxConstraints(),
                                      ),
                                      const SizedBox(width: 2),
                                      IconButton(
                                        constraints: const BoxConstraints(),
                                        padding:
                                            const EdgeInsets.only(bottom: 2),
                                        icon: OfflineUtils().isFavourite(
                                                widget.favouritesResponse,
                                                widget.currentItem.prchOrdrId
                                                    .toString())
                                            ? const Icon(
                                                Icons.favorite,
                                                color: AppColors.yellowColor,
                                              )
                                            : const Icon(
                                                Icons.favorite_border_outlined),
                                        onPressed: () {
                                          if (!OfflineUtils().isFavourite(
                                              widget.favouritesResponse,
                                              widget.currentItem.prchOrdrId
                                                  .toString())) {
                                            context.read<OfflineMainBloc>().add(
                                                  MarkAsFavourite(
                                                    getCustomerId(),
                                                    widget
                                                        .currentItem.prchOrdrId
                                                        .toString(),
                                                    widget.categoryType,
                                                  ),
                                                );
                                          } else {
                                            context.read<OfflineMainBloc>().add(
                                                  UnMarkAsFavourite(
                                                    getCustomerId(),
                                                    widget
                                                        .currentItem.prchOrdrId
                                                        .toString(),
                                                    widget.categoryType,
                                                  ),
                                                );
                                          }
                                        },
                                      ),
                                      const SizedBox(width: 2),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 2),
                                SizedBox(
                                  width: kIsWeb
                                      ? 400
                                      : MediaQuery.of(context).size.width - 100,
                                  child: GestureDetector(
                                    onTap: () {
                                      Get.to(
                                        MrGrouping(
                                          itemName: widget
                                                  .currentItem.orderGroupName ??
                                              "",
                                          itemId: widget
                                              .currentItem.orderGroupId
                                              .toString(),
                                        ),
                                      );
                                    },
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: Text(
                                            widget.currentItem.orderGroupName ??
                                                "",
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 14.0,
                                              // color: AppColors.primaryColor,
                                            ),
                                            maxLines: 2,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 4),
                                // RatingBarIndicator(
                                //   rating: widget.currentItem.vendorRating?.toDouble() ?? 0,
                                //   itemBuilder: (context, index) => const Icon(
                                //     Icons.star,
                                //     color: Colors.amber,
                                //   ),
                                //   itemCount: 5,
                                //   itemSize: 15.0,
                                //   unratedColor: Colors.grey,
                                //   direction: Axis.horizontal,
                                // ),
                                // SizedBox(
                                //   width: MediaQuery.of(context).size.width - 100,
                                //   child: Row(
                                //     children: [
                                //       Expanded(
                                //         child: Text(
                                //           widget.currentItem.statusName ?? "",
                                //           style: const TextStyle(
                                //             fontWeight: FontWeight.bold,
                                //             fontSize: 14.0,
                                //             color: Colors.black,
                                //           ),
                                //           maxLines: 2,
                                //         ),
                                //       ),
                                //     ],
                                //   ),
                                // ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 4,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ////
                            isBuyer()
                                ? Column(
                                    children: [
                                      //vishnu-commented

                                      const SizedBox(height: 4),
                                      EnquiryKeyValue(
                                          key1: "Created By:",
                                          value: widget.currentItem.createdBy ??
                                              "N/A"),
                                      const SizedBox(height: 4),

                                      EnquiryKeyValue(
                                          key1: "Site Name:",
                                          value:
                                              widget.currentItem.projectName ??
                                                  "N/A"),
                                      const SizedBox(height: 4),
                                      EnquiryKeyValue(
                                          key1: "Split Name:",
                                          value: widget.currentItem
                                                  .prchOrdrSplitName ??
                                              "N/A"),
                                      const SizedBox(height: 4),
                                      EnquiryKeyValue(
                                          key1: "Category:",
                                          value: widget.currentItem
                                                  .cappCategoriesName ??
                                              "N/A"),
                                      if (showQuantity())
                                        const SizedBox(height: 4),
                                      if (showQuantity())
                                        EnquiryKeyValue(
                                            key1: "Quantity:",
                                            value:
                                                "${widget.currentItem.quantity?.toStringAsFixed(0)} ${widget.currentItem.optionName ?? ""}" ??
                                                    "N/A"),
                                      // const SizedBox(height: 4),
                                      // EnquiryKeyValue(key1: "Product Name:", value: widget.currentItem.mvtItemName ?? "N/A"),
                                      const SizedBox(height: 4),
                                      EnquiryKeyValue(
                                        key1: "Status:",
                                        value: widget.currentItem.statusName ??
                                            "N/A",
                                        color: enableSubmit
                                            ? Colors.green.shade200
                                            : Colors.transparent,
                                        bold: true,
                                      ),
                                    ],
                                  )
                                : Column(
                                    children: [
                                      // const SizedBox(height: 4),
                                      // EnquiryKeyValue(key1: "Product Name:", value: widget.currentItem.mvtItemName ?? "N/A"),
                                      if (showQuantity())
                                        const SizedBox(height: 4),
                                      if (showQuantity())
                                        EnquiryKeyValue(
                                            key1: "Quantity:",
                                            value:
                                                "${widget.currentItem.quantity?.toStringAsFixed(0)} ${widget.currentItem.optionName ?? ""}" ??
                                                    "N/A"),
                                      const SizedBox(height: 4),
                                      EnquiryKeyValue(
                                          key1: "Category:",
                                          value: widget.currentItem
                                                  .cappCategoriesName ??
                                              "N/A"),
                                      const SizedBox(height: 4),

                                      EnquiryKeyValue(
                                        key1: "Status:",
                                        value: widget.currentItem.statusName ??
                                            "N/A",
                                        color: enableSubmit
                                            ? Colors.green.shade200
                                            : Colors.transparent,
                                        bold: true,
                                      ),
                                      const SizedBox(height: 4),
                                      // InkWell(
                                      //   onTap: () {
                                      //     showDialog(
                                      //       context: context,
                                      //       builder: (BuildContext context) {
                                      //         return const UserProfileDialog();
                                      //       },
                                      //     );
                                      //   },
                                      //   child: EnquiryKeyValue(
                                      //       key1: "Buyer Name:",
                                      //       value: (widget.mode == 2 ? widget.currentItem.buyerCustomerName : widget.currentItem.customerName) ?? "N/A"),
                                      // ),
                                      // const SizedBox(height: 4),
                                      InkWell(
                                          onTap: () {
                                            showDialog(
                                              context: context,
                                              builder: (BuildContext context) {
                                                return UserProfileDialog(
                                                    vendorId: widget
                                                        .currentItem.vendorId,
                                                    prchOrdrId: widget
                                                        .currentItem
                                                        .prchOrdrId);
                                              },
                                            );
                                          },
                                          child: EnquiryKeyValue(
                                              key1: "Buyer Company:",
                                              value: widget
                                                      .currentItem.vendorName ??
                                                  "N/A")),
                                      const SizedBox(height: 4),
                                      InkWell(
                                        onTap: () {
                                          makePhoneCall((widget.mode == 2
                                                  ? widget.currentItem
                                                      .buyerCustomerPhone
                                                  : widget.currentItem
                                                      .customerPhone) ??
                                              "");
                                        },
                                        child: EnquiryKeyValue(
                                            key1: "Buyer Phone:",
                                            value: (widget.mode == 2
                                                    ? widget.currentItem
                                                        .buyerCustomerPhone
                                                    : widget.currentItem
                                                        .customerPhone) ??
                                                "N/A"),
                                      ),
                                    ],
                                  ),
                            const SizedBox(height: 4),
                            EnquiryKeyValue(
                                key1: "Created On:",
                                value: widget.currentItem.createdAt != null
                                    ? widget.currentItem.createdAt!
                                        .toCreatedOn()
                                    : ''),
                            const SizedBox(height: 4),
                            EnquiryKeyValue(
                                key1: "Delivery On:",
                                value: widget.currentItem.deliveryDate != null
                                    ? widget.currentItem.deliveryDate!
                                        .toDeliveryOn()
                                    : ''),
                            if (widget.currentItem.variant1Options != null &&
                                widget.currentItem.variant1OptionGroupName !=
                                    null &&
                                widget.currentItem.variant1OptionGroupName!
                                    .isNotEmpty) ...[
                              const SizedBox(height: 4),
                              EnquiryKeyValue(
                                key1:
                                    "${widget.currentItem.variant1OptionGroupName}:",
                                value: widget.currentItem.variant1Options
                                        ?.addSpaceAfterCommas() ??
                                    "N/A",
                              ),
                            ],
                            if (widget.currentItem.variant2Options != null &&
                                widget.currentItem.variant2OptionGroupName !=
                                    null &&
                                widget.currentItem.variant2OptionGroupName !=
                                    widget.currentItem
                                        .variant1OptionGroupName) ...[
                              const SizedBox(height: 4),
                              EnquiryKeyValue(
                                key1:
                                    "${widget.currentItem.variant2OptionGroupName}:",
                                value: widget.currentItem.variant2Options
                                        ?.addSpaceAfterCommas() ??
                                    "N/A",
                              ),
                            ],
                            if (widget.currentItem.variant3Options != null &&
                                widget.currentItem.variant3OptionGroupName !=
                                    null &&
                                widget.currentItem.variant3OptionGroupName !=
                                    widget
                                        .currentItem.variant1OptionGroupName &&
                                widget.currentItem.variant3OptionGroupName !=
                                    widget.currentItem
                                        .variant2OptionGroupName) ...[
                              const SizedBox(height: 4),
                              EnquiryKeyValue(
                                key1:
                                    "${widget.currentItem.variant3OptionGroupName}:",
                                value: widget.currentItem.variant3Options
                                        ?.addSpaceAfterCommas() ??
                                    "N/A",
                              ),
                            ],
                            const SizedBox(height: 4),
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 1),
                              child: Row(
                                children: [
                                  const Expanded(
                                    flex: 2,
                                    child: Text(
                                      "Attachments:",
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16),
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    flex: 3,
                                    child: Align(
                                      alignment: Alignment.centerLeft,
                                      child: Row(
                                        children: [
                                          if (images.isNotEmpty)
                                            Icon(
                                              Icons.image_outlined,
                                              size: 16,
                                              color: AppColors.primaryColor,
                                            ),
                                          if (audios.isNotEmpty)
                                            const SizedBox(width: 6),
                                          if (audios.isNotEmpty)
                                            Icon(
                                              Icons.play_arrow_outlined,
                                              size: 20,
                                              color: AppColors.primaryColor,
                                            ),
                                          if (files.isNotEmpty)
                                            const SizedBox(width: 6),
                                          if (files.isNotEmpty)
                                            Icon(
                                              Icons.file_copy_outlined,
                                              size: 16,
                                              color: AppColors.primaryColor,
                                            ),
                                          if (images.isEmpty &&
                                              audios.isEmpty &&
                                              files.isEmpty)
                                            const Text(
                                              "N/A",
                                              style: TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (!isBuyer() && widget.mode == 2)
                              Column(
                                children: [
                                  const SizedBox(height: 4),
                                  // EnquiryKeyValue(
                                  //   key1: "Buyer Name:",
                                  //   value: widget.currentItem.buyerVendorName ?? "N/A",
                                  // ),
                                  // const SizedBox(height: 4),
                                  // EnquiryKeyValue(
                                  //   key1: "Buyer Phone:",
                                  //   value: widget.currentItem.buyerCustomerPhone ?? "N/A",
                                  // ),
                                  // const SizedBox(height: 4),
                                  EnquiryKeyValue(
                                    key1: "Offer Price:",
                                    value: widget.currentItem.offerPrice
                                            ?.toInt()
                                            .toString() ??
                                        "N/A",
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 2),
                      Stack(
                        children: [
                          Column(
                            children: [
                              //level 2
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Row(
                                  children: [
                                    if (showEnquiryClosesOn())
                                      Expanded(
                                        flex: 1,
                                        child: Padding(
                                          padding: const EdgeInsets.all(2),
                                          child: Container(
                                            height: 48,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              border: Border.all(
                                                width: 1.0,
                                                color: Colors.black,
                                              ),
                                            ),
                                            child: Center(
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(2),
                                                child: AutoSizeText(
                                                  "Request Due Date:\n${TimeUtils().formatBaiDate(DateFormat('yyyy-MM-dd HH:mm:ss').format(widget.currentItem.deliveryDate ?? DateTime.now()))}",
                                                  textAlign: TextAlign.center,
                                                  style: const TextStyle(
                                                    color: Colors.red,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    if (showEnquiryClosesOn())
                                      const SizedBox(width: 4),
                                    Expanded(
                                      flex: 1,
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Container(
                                          height: 48,
                                          decoration: BoxDecoration(
                                            color: AppColors.green,
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: TextButton(
                                            style: const ButtonStyle(
                                                // Change text color
                                                ),
                                            onPressed: () {
                                              context
                                                  .read<OfflineMainBloc>()
                                                  .add(ItemSelected(widget
                                                          .currentItem
                                                          .prchOrdrId
                                                          ?.toInt() ??
                                                      0));
                                              context
                                                  .read<OfflineSubBloc>()
                                                  .add(const ToggleCard());
                                              setState(() {
                                                openData();

                                                isRead = true;
                                              });
                                            },
                                            child: Text(
                                              parentState.buttonText,
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              // copied from stocks
                              if (parentState.isOpen)
                                BaiOfflineAttachments(item: widget.currentItem),
                              // last row
                              const SizedBox(height: 2),
                              // if (OfflineBidManager().isAvailable(firebaseResponse) && parentState.isOpen && widget.mode == 1)
                              //   BiddingInfoWidgetPrimary(
                              //     currentItem: widget.currentItem,
                              //     parentState: parentState,
                              //     cardContext: context,
                              //     firebaseResponse: firebaseResponse,
                              //     showActiveBuyers: showActiveBuyerCount,
                              //     margin: format.format(margin),
                              //   ),

                              if (parentState.isOpen &&
                                  (!isBuyer() ||
                                      (isBuyer() &&
                                          (getRoleLevel() != 1) &&
                                          (getRoleLevel() != 5) &&
                                          getRoleLevel() != 10)))
                                BiddingInfoWidgetHistoryOffersMore(
                                  content: widget.currentItem,
                                  reload: () {
                                    context.read<OfflineMainBloc>().add(
                                          InitializeOfflineStocks(
                                            offlineStocks: BaiProductsRes(),
                                            queryString: widget.query,
                                            code: widget.categoryType,
                                          ),
                                        );
                                  },
                                  minimize: () {
                                    // context
                                    //     .read<OfflineSubBloc>()
                                    //     .add(const ToggleCard());
                                  },
                                ),
                              if (parentState.isOpen &&
                                  ((getRoleLevel() == 1) ||
                                      (getRoleLevel() == 5) ||
                                      (getRoleLevel() == 10)) &&
                                  isBuyer())
                                BiddingInfoWidgetEditAssignApprove(
                                  content: widget.currentItem,
                                  changeStatus: () async {
                                    try {
                                      var api = NetworkController();
                                      var res = await api.changeStatus(widget
                                          .currentItem.prchOrdrId
                                          .toString());
                                      if (res.status == 200) {
                                        alert(res.statusDescription);
                                        context.read<OfflineMainBloc>().add(
                                              InitializeOfflineStocks(
                                                offlineStocks: BaiProductsRes(),
                                                queryString: widget.query,
                                                code: widget.categoryType,
                                              ),
                                            );
                                      } else {
                                        alert(res.statusDescription);
                                      }
                                    } catch (e) {
                                      // alert("Failed to change status, please try again later!");
                                    }
                                  },
                                  reload: () async {
                                    context.read<OfflineMainBloc>().add(
                                          InitializeOfflineStocks(
                                            offlineStocks: BaiProductsRes(),
                                            queryString: widget.query,
                                            code: widget.categoryType,
                                          ),
                                        );
                                  },
                                ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        } else {
          return Center(
            child: SizedBox(
              height: 72,
              child: Text(
                "Loading...",
                style: TextStyle(color: AppColors.primaryColor),
              ),
            ),
          );
        }
      },
    );
  }

  bool showEnquiryClosesOn() {
    return widget.mode != 2;
  }

  @override
  bool get wantKeepAlive => true;

  openData() async {
    isOpen = !isOpen;
    if (buttonText == "Open") {
      buttonText = "Close";
      try {
        await NetworkController().saveUserAction(UserActionReq(
          action: "PO/PE_SO",
          customerId: int.tryParse(getCustomerId()) ?? 0,
          actionTime: DateTime.now().toUtc(),
          stockId: widget.currentItem.prchOrdrId?.toInt() ?? 0,
          deviceType: "mobile",
          screenName: "PO / MR",
          vendorId: getVendorId(),
        ));
        await NetworkController().markAsRead(
          widget.currentItem.prchOrdrId?.toInt() ?? 0,
        );
      } catch (e) {
        safePrint(e);
      }
    } else {
      buttonText = "Open";
    }
    isOpen ? firebaseListener() : null;
  }
}

class BaiOfflineAttachments extends StatefulWidget {
  const BaiOfflineAttachments({
    Key? key,
    required this.item,
  }) : super(key: key);

  final Content item;

  @override
  State<BaiOfflineAttachments> createState() => _BaiOfflineAttachmentsState();
}

class _BaiOfflineAttachmentsState extends State<BaiOfflineAttachments> {
  List<String> urls = [];
  List<String> images = [];
  List<String> files = [];
  List<String> audios = [];

  bool playing = false;

  final player = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _setArrays();
  }

  void _setArrays() {
    final item = widget.item;

    urls = [
      if (item.image1 != null) item.image1!,
      if (item.image2 != null) item.image2!,
      if (item.image3 != null) item.image3!,
      if (item.image4 != null) item.image4!,
      if (item.image5 != null) item.image5!,
    ];

    for (var url in urls) {
      if (_isImage(url)) {
        images.add(url);
      } else if (_isAudio(url)) {
        audios.add(url);
      } else {
        files.add(url);
      }
    }
  }

  static bool _isImage(String url) {
    return url.endsWith('.jpg') ||
        url.endsWith('.jpeg') ||
        url.endsWith('.png') ||
        url.endsWith('.gif') ||
        url.endsWith('.bmp');
  }

  static bool _isAudio(String url) {
    return url.endsWith('.mp3') ||
        url.endsWith('.wav') ||
        url.endsWith('.aac') ||
        url.endsWith('.ogg') ||
        url.endsWith('.mp4');
  }

  Future<void> _togglePlayback() async {
    try {
      if (playing) {
        await player.stop();
        setState(() {
          playing = false;
        });
      } else {
        await player.play(UrlSource(audios[0]));
        setState(() {
          playing = true;
        });
        player.onPlayerComplete.listen((event) {
          setState(() {
            playing = false;
          });
        });
      }
    } catch (e) {
      alert(e.toString());
    }
  }

  @override
  void dispose() {
    player.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Photos
            if (images.isNotEmpty)
              const Text(
                'Photos',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            if (images.isNotEmpty) const SizedBox(height: 8.0),
            if (images.isNotEmpty)
              SizedBox(
                height: 74,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: images.length,
                  shrinkWrap: true,
                  padding: const EdgeInsets.all(2),
                  itemBuilder: (context, index) {
                    return Material(
                      elevation: 2,
                      borderRadius: BorderRadius.circular(8.0),
                      clipBehavior: Clip.hardEdge,
                      child: GestureDetector(
                        onTap: () {
                          showImageDialog(context, images[index]);
                        },
                        child: Container(
                          margin: const EdgeInsets.all(0.25),
                          child: Image.network(
                            images[index],
                            width: 72.0,
                            height: 72.0,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (BuildContext context, int index) {
                    return const SizedBox(width: 8);
                  },
                ),
              ),

            if (images.isNotEmpty) const SizedBox(height: 16.0),

            // Documents
            if (files.isNotEmpty)
              const Text(
                'Documents',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            if (files.isNotEmpty) const SizedBox(height: 8.0),
            if (files.isNotEmpty)
              SizedBox(
                height: 74,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: files.length,
                  shrinkWrap: true,
                  padding: const EdgeInsets.all(2),
                  itemBuilder: (context, index) {
                    var url = Uri.parse(files[index]);
                    return GestureDetector(
                      onTap: () async {
                        if (await canLaunchUrl(url)) {
                          await launchUrl(url);
                        } else {
                          throw 'Could not launch $url';
                        }
                      },
                      child: Material(
                        elevation: 2,
                        borderRadius: BorderRadius.circular(8.0),
                        clipBehavior: Clip.hardEdge,
                        child: Container(
                          color: Colors.grey.shade200,
                          height: 72,
                          width: 72,
                          margin: const EdgeInsets.all(0.25),
                          child: Center(
                              child: Icon(Icons.file_copy_outlined,
                                  size: 32.0, color: AppColors.primaryColor)),
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (BuildContext context, int index) {
                    return const SizedBox(width: 8);
                  },
                ),
              ),

            if (files.isNotEmpty) const SizedBox(height: 16.0),

            // Voice notes
            if (audios.isNotEmpty)
              const Text(
                'Voice notes',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            if (audios.isNotEmpty) const SizedBox(height: 8.0),
            if (audios.isNotEmpty)
              Row(
                children: [
                  GestureDetector(
                    onTap: _togglePlayback,
                    child: Icon(playing ? Icons.pause : Icons.play_circle_fill,
                        size: 40.0, color: AppColors.primaryColor),
                  ),
                  const SizedBox(width: 8.0),
                  Container(
                    width: 200.0,
                    height: 40.0,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    child: Image.asset("assets/images/waveform.png"),
                  ),
                ],
              ),
            if (audios.isNotEmpty) const SizedBox(height: 16.0),
            // Instructions
            if (!(widget.item.instructions == null ||
                widget.item.instructions == ""))
              const Text(
                'Instructions',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            if (!(widget.item.instructions == null ||
                widget.item.instructions == ""))
              const SizedBox(height: 8.0),
            if (!(widget.item.instructions == null ||
                widget.item.instructions == ""))
              Text(
                (widget.item.instructions == null ||
                        widget.item.instructions == "")
                    ? "No instructions available"
                    : widget.item.instructions ?? "N/A",
                style: const TextStyle(color: Colors.black),
              ),
            if (!(widget.item.instructions == null ||
                widget.item.instructions == ""))
              const SizedBox(height: 8.0),
          ],
        ),
      ),
    );
  }
}

class BiddingInfoWidgetPrimary extends StatefulWidget {
  const BiddingInfoWidgetPrimary({
    Key? key,
    required this.currentItem,
    required this.parentState,
    required this.cardContext,
    required this.firebaseResponse,
    required this.margin,
    required this.showActiveBuyers,
    required this.close,
  }) : super(key: key);

  final Content currentItem;
  final OfflineCardLoaded parentState;
  final BuildContext cardContext;
  final FirebaseResponseOffline firebaseResponse;
  final String margin;
  final bool showActiveBuyers;
  final Function close;

  @override
  State<BiddingInfoWidgetPrimary> createState() =>
      _BiddingInfoWidgetPrimaryState();
}

class _BiddingInfoWidgetPrimaryState extends State<BiddingInfoWidgetPrimary> {
  num lastBid = 0.0;

  @override
  Widget build(BuildContext context) {
    var desc = '';
    if (widget.firebaseResponse.stockStatusDescription == null) {
      desc == "null";
    }
    return Visibility(
      // visible: widget.parentState.isOpen && OfflineBidManager().getVisibility(widget.firebaseResponse),
      visible: true,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(12.0),
            bottomRight: Radius.circular(12.0),
          ),
          color: OfflineBidManager().getColor(widget.firebaseResponse),
        ),
        child: Column(
          children: [
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Material(
                      elevation: 8,
                      borderRadius: BorderRadius.circular(8.0),
                      child: Container(
                        height: 72,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8.0),
                          color: Colors.white,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "₹${widget.firebaseResponse.highestBid}",
                              style: const TextStyle(
                                color: Colors.red,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            // const SizedBox(height: 2.0),
                            const Text(
                              "Current Offer",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: GestureDetector(
                      onTap: () async {
                        if (await FirebaseManager().isAvailable(
                            widget.currentItem.prchOrdrId.toString())) {
                          final offlineCardBloc =
                              context.read<OfflineSubBloc>();
                          context
                              .read<OfflineSubBloc>()
                              .add(const ToggleMoreOptions(true));
                          // showDialog(
                          //     context: widget.cardContext,
                          //     builder: (BuildContext latestContext) {
                          //       return BlocProvider<OfflineSubBloc>.value(
                          //         value: offlineCardBloc,
                          //         child: MoreOptionsPopup(
                          //           title: "More Options - Lot No ${widget.currentItem.lotNo}",
                          //           currentItem: widget.currentItem,
                          //           firebaseResponse: widget.firebaseResponse,
                          //           parentContext: latestContext,
                          //         ),
                          //       );
                          //     });
                        }
                      },
                      child: Material(
                        elevation: 8,
                        borderRadius: BorderRadius.circular(8.0),
                        child: Container(
                          height: 72,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.0),
                            color: Colors.white,
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                "₹${widget.firebaseResponse.highestBid}",
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              // const SizedBox(height: 2.0),
                              const Text(
                                "Your Offer",
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Material(
                      elevation: 8,
                      borderRadius: BorderRadius.circular(8.0),
                      clipBehavior: Clip.hardEdge,
                      color: Colors.white,
                      child: InkWell(
                        onTap: () async {
                          if (getRoleLevel() == 15) {
                            alert("For quotes, please contact the manager.");
                            return;
                          }

                          // if (!isBuyer()) {
                          //   Get.to(SellerOffersPage(item: widget.currentItem));
                          // } else {
                          //   Get.to(BuyerOffersPage(item: widget.currentItem));
                          // }
                        },
                        onLongPress: () {
                          // Get.to(SellerOffers(item: widget.currentItem));
                        },
                        child: SizedBox(
                          height: 72,
                          child: Center(
                            child: Text(
                              !isBuyer() ? "Submit Offer" : "View Offers",
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  showActiveBuyerInfo()
                      ? Visibility(
                          visible: widget.showActiveBuyers,
                          child: ActiveBuyerCount(
                            stockId: widget.parentState.currentItem,
                            screenType: FromScreen.offline,
                          ),
                        )
                      : const SizedBox.shrink(),
                  const Spacer(),
                  Text.rich(
                    TextSpan(
                      children: [
                        const TextSpan(
                          text: "Total : ",
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                            // fontFamily: "avenir-black",
                          ),
                        ),
                        TextSpan(
                          text:
                              "₹${widget.currentItem.quantity!.toInt() * widget.firebaseResponse.highestBid}",
                          style: const TextStyle(
                            color: Colors.yellow,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                            // fontFamily: "avenir-black",
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// ignore: must_be_immutable
class OfflineRowValues extends StatelessWidget {
  OfflineRowValues(
      {Key? key, required this.currentItem, required this.firebaseResponse})
      : super(key: key);

  final Content currentItem;
  final FirebaseResponseOffline firebaseResponse;

  List<String> urls = [];
  List<String> images = [];
  List<String> files = [];
  List<String> audios = [];

  bool playing = false;

  final player = AudioPlayer();

  void _setArrays() {
    final item = currentItem;

    urls = [
      if (item.image1 != null) item.image1!,
      if (item.image2 != null) item.image2!,
      if (item.image3 != null) item.image3!,
      if (item.image4 != null) item.image4!,
      if (item.image5 != null) item.image5!,
    ];

    for (var url in urls) {
      if (_isImage(url)) {
        images.add(url);
      } else if (_isAudio(url)) {
        audios.add(url);
      } else {
        files.add(url);
      }
    }
  }

  static bool _isImage(String url) {
    return url.endsWith('.jpg') ||
        url.endsWith('.jpeg') ||
        url.endsWith('.png') ||
        url.endsWith('.gif') ||
        url.endsWith('.bmp');
  }

  static bool _isAudio(String url) {
    return url.endsWith('.mp3') ||
        url.endsWith('.wav') ||
        url.endsWith('.aac') ||
        url.endsWith('.ogg') ||
        url.endsWith('.mp4');
  }

  @override
  Widget build(BuildContext context) {
    _setArrays();
    return Expanded(
      flex: 5,
      child: Column(
        children: [
          ProductValues(currentItem.quantity.toString(), false),
          ProductValues(
              currentItem.deliveryDate != null
                  ? '${currentItem.deliveryDate!.year.toString().padLeft(4, '0')}-${currentItem.deliveryDate!.month.toString().padLeft(2, '0')}-${currentItem.deliveryDate!.day.toString().padLeft(2, '0')}'
                  : '',
              false),
          ProductValues(
              currentItem.deliveryDate != null
                  ? '${currentItem.deliveryDate!.year.toString().padLeft(4, '0')}-${currentItem.deliveryDate!.month.toString().padLeft(2, '0')}-${currentItem.deliveryDate!.day.toString().padLeft(2, '0')}'
                  : '',
              false),
          ProductValues(currentItem.deliveryCity ?? "", false),
          ProductValues(currentItem.deliveryCity ?? "", false),
          const ProductValues("9991112222" ?? "", false),
          Expanded(
            child: Align(
              alignment: Alignment.centerLeft,
              child: ListView(
                scrollDirection: Axis.horizontal,
                shrinkWrap: true,
                children: [
                  // Icon(
                  //   Icons.edit_note_outlined,
                  //   size: 20,
                  //   color: AppColors.primaryColor,
                  // ),
                  // const SizedBox(width: 6),
                  if (images.isNotEmpty)
                    Icon(
                      Icons.image_outlined,
                      size: 16,
                      color: AppColors.primaryColor,
                    ),
                  if (audios.isNotEmpty) const SizedBox(width: 6),
                  if (audios.isNotEmpty)
                    Icon(
                      Icons.play_arrow_outlined,
                      size: 20,
                      color: AppColors.primaryColor,
                    ),
                  if (files.isNotEmpty) const SizedBox(width: 6),
                  if (files.isNotEmpty)
                    Icon(
                      Icons.file_copy_outlined,
                      size: 16,
                      color: AppColors.primaryColor,
                    ),
                  if (images.isEmpty && audios.isEmpty && files.isEmpty)
                    const Text(
                      "N/A",
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class OfflineRowDots extends StatelessWidget {
  const OfflineRowDots({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Expanded(
      flex: 1,
      child: Column(
        children: [
          Expanded(child: Center(child: Text(":"))),
          Expanded(child: Center(child: Text(":"))),
          Expanded(child: Center(child: Text(":"))),
          Expanded(child: Center(child: Text(":"))),
          Expanded(child: Center(child: Text(":"))),
          Expanded(child: Center(child: Text(":"))),
          Expanded(child: Center(child: Text(":"))),
        ],
      ),
    );
  }
}

class OfflineRowKeys extends StatelessWidget {
  const OfflineRowKeys({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Expanded(
      flex: 4,
      child: Column(
        children: [
          ProductKeys("Quantity"),
          ProductKeys("Created on"),
          ProductKeys("Delivery on"),
          ProductKeys("Road Access"),
          ProductKeys("Location"),
          ProductKeys("Phone"),
          ProductKeys("Attachments"),
        ],
      ),
    );
  }
}

class OfflineRowImage extends StatelessWidget {
  OfflineRowImage(
      {Key? key, required this.currentItem, required this.favouritesResponse})
      : super(key: key);

  final Content currentItem;
  final FavouritesResponse favouritesResponse;
  List<String> urls = [];
  List<String> images = [];

  bool playing = false;

  final player = AudioPlayer();

  void _setArrays() {
    final item = currentItem;

    urls = [
      if (item.image1 != null) item.image1!,
      if (item.image2 != null) item.image2!,
      if (item.image3 != null) item.image3!,
      if (item.image4 != null) item.image4!,
      if (item.image5 != null) item.image5!,
    ];

    for (var url in urls) {
      if (_isImage(url)) {
        images.add(url);
      }
    }
  }

  static bool _isImage(String url) {
    return url.endsWith('.jpg') ||
        url.endsWith('.jpeg') ||
        url.endsWith('.png') ||
        url.endsWith('.gif') ||
        url.endsWith('.bmp');
  }

  @override
  Widget build(BuildContext context) {
    _setArrays();
    var dp = currentItem.mvtItemImage;
    return Expanded(
      flex: 5,
      child: Column(
        children: [
          Stack(
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 0, left: 8),
                child: SizedBox(
                  height: 96,
                  width: 96,
                  child: Card(
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(
                        Radius.circular(12.0),
                      ),
                    ),
                    clipBehavior: Clip.hardEdge,
                    elevation: 2.0,
                    child: GestureDetector(
                      onTap: () {
                        showImageDialog(context, dp);
                      },
                      child: Center(
                        child: dp != null
                            ? Image.network(
                                dp,
                                fit: BoxFit.fill,
                              )
                            : Image.asset(
                                'assets/images/no_image_available.jpeg',
                                fit: BoxFit.fill,
                              ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            currentItem.prchOrdrId.toString(),
            style: const TextStyle(
              fontSize: 12.0,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            "Lot No ${currentItem.projectId}",
            style: const TextStyle(
              fontSize: 14.0,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          GestureDetector(
            onTap: () {
              showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return const UserProfileDialog(stockId: "");
                  });
            },
            child: SizedBox(
              width: 100,
              child: Text(
                currentItem.vendorId?.toString() ?? "",
                textAlign: TextAlign.center,
                maxLines: 1,
                style: const TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: AppColors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 4),
          GestureDetector(
            onTap: () {
              showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return CustomerRating(
                      context,
                      sellerId: '',
                    );
                  });
            },
            child: RatingBarIndicator(
              rating:
                  double.parse(currentItem.averageRating?.toString() ?? "0"),
              itemBuilder: (context, index) => const Icon(
                Icons.star,
                color: Colors.amber,
              ),
              itemCount: 5,
              itemSize: 15.0,
              unratedColor: Colors.grey,
              direction: Axis.horizontal,
            ),
          ),
        ],
      ),
    );
  }
}

class ProductValues extends StatelessWidget {
  final String productValue;
  final bool isGreen;

  const ProductValues(this.productValue, this.isGreen, {Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    var color = Colors.black;
    if (isGreen) {
      color = AppColors.green;
    }
    return Expanded(
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          productValue,
          maxLines: 1,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }
}

class ProductKeys extends StatelessWidget {
  final String productKey;

  const ProductKeys(this.productKey, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.only(left: 16.0),
        child: Align(
          alignment: Alignment.centerLeft,
          child: Text(
            productKey,
            style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.black,
                overflow: TextOverflow.ellipsis),
          ),
        ),
      ),
    );
  }
}

class SearchPopup extends StatefulWidget {
  const SearchPopup({Key? key}) : super(key: key);

  @override
  State<SearchPopup> createState() => _SearchPopupState();
}

class _SearchPopupState extends State<SearchPopup> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Search by ID',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryColor,
              ),
            ),
            const SizedBox(height: 24),
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Enter ID',
                filled: true,
                fillColor: Colors.grey[100],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: AppColors.primaryColor,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 14,
                ),
              ),
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 32),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text(
                    'Cancel',
                    style: TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    final searchTerm = _searchController.text;
                    Navigator.pop(context, searchTerm);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                  ),
                  child: const Text(
                    'Search',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
