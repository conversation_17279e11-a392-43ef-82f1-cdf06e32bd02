// import 'dart:convert';

// class CalculatorRes {
//   Data? data;
//   int? status;
//   String? statusDescription;

//   CalculatorRes({
//     this.data,
//     this.status,
//     this.statusDescription,
//   });

//   factory CalculatorRes.fromRawJson(String str) =>
//       CalculatorRes.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory CalculatorRes.fromJson(Map<String, dynamic> json) => CalculatorRes(
//         data: json["data"] == null ? null : Data.fromJson(json["data"]),
//         status: json["status"],
//         statusDescription: json["status_description"],
//       );

//   Map<String, dynamic> toJson() => {
//         "data": data?.toJson(),
//         "status": status,
//         "status_description": statusDescription,
//       };
// }

// class Data {
//   num? prchOrdrOfferId;
//   num? gst;
//   num? transportationCharge;
//   num? totalDiscount;
//   num? total;
//   num? subtotal1;
//   num? subtotal2;
//   num? priceWtg;
//   num? discountedPrice;
//   num? discountedGst;

//   Data({
//     this.prchOrdrOfferId,
//     this.gst,
//     this.transportationCharge,
//     this.totalDiscount,
//     this.total,
//     this.subtotal1,
//     this.subtotal2,
//     this.priceWtg,
//     this.discountedPrice,
//     this.discountedGst,
//   });

//   factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Data.fromJson(Map<String, dynamic> json) => Data(
//         prchOrdrOfferId: json["prchOrdrOfferId"],
//         gst: json["gst"],
//         transportationCharge: json["transportationCharge"],
//         totalDiscount: json["totalDiscount"],
//         total: json["total"],
//         subtotal1: json["subtotal1"],
//         subtotal2: json["subtotal2"],
//         priceWtg: json["priceWtg"],
//         discountedPrice: json["discountedPrice"],
//         discountedGst: json["discountedGst"],
//       );

//   Map<String, dynamic> toJson() => {
//         "prchOrdrOfferId": prchOrdrOfferId,
//         "gst": gst,
//         "transportationCharge": transportationCharge,
//         "totalDiscount": totalDiscount,
//         "total": total,
//         "subtotal1": subtotal1,
//         "subtotal2": subtotal2,
//         "priceWtg": priceWtg,
//         "discountedPrice": discountedPrice,
//         "discountedGst": discountedGst,
//       };
// }

import 'dart:convert';

class CalculatorRes {
  Data? data;
  int? status;
  String? statusDescription;

  CalculatorRes({
    this.data,
    this.status,
    this.statusDescription,
  });

  factory CalculatorRes.fromRawJson(String str) =>
      CalculatorRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CalculatorRes.fromJson(Map<String, dynamic> json) => CalculatorRes(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
        "status": status,
        "status_description": statusDescription,
      };
}

class Data {
  num? subTotalWithoutGst;
  num? discount;
  num? totalAfterDiscount;
  num? gst;
  num? transportation;
  num? loading;
  num? gstOnTransportation;
  num? total;

  Data({
    this.subTotalWithoutGst,
    this.discount,
    this.totalAfterDiscount,
    this.gst,
    this.transportation,
    this.loading,
    this.gstOnTransportation,
    this.total,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        subTotalWithoutGst: json["subTotalWithoutGst"],
        discount: json["discount"],
        totalAfterDiscount: json["totalAfterDiscount"],
        gst: json["gst"],
        transportation: json["transportation"],
        loading: json["loading"],
        gstOnTransportation: json["gstOnTransportation"],
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "subTotalWithoutGst": subTotalWithoutGst,
        "discount": discount,
        "totalAfterDiscount": totalAfterDiscount,
        "gst": gst,
        "transportation": transportation,
        "loading": loading,
        "gstOnTransportation": gstOnTransportation,
        "total": total,
      };
}
