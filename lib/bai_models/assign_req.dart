import 'dart:convert';

import 'package:intl/intl.dart';

AssignReq assignReqFromJson(String str) => AssignReq.fromJson(json.decode(str));

String assignReqToJson(AssignReq data) => json.encode(data.toJson());

class AssignReq {
  int? customerId;
  int? insertedCustomerId;
  int? insertedVendorId;
  int? purchaseOrderId;
  int orderGrpId;
  List<int>? vendorsId;
  DateTime deliveryDate;
  int? prchOrdrSplitId;

  AssignReq({
    this.customerId,
    this.insertedCustomerId,
    this.insertedVendorId,
    this.purchaseOrderId,
    this.vendorsId,
    this.prchOrdrSplitId,
    required this.deliveryDate,
    required this.orderGrpId,
  });

  factory AssignReq.fromJson(Map<String, dynamic> json) => AssignReq(
        customerId: json["customer_id"],
        insertedCustomerId: json["inserted_customer_id"],
        insertedVendorId: json["inserted_vendor_id"],
        purchaseOrderId: json["purchase_order_id"],
        orderGrpId: json["orderGrpId"],
        vendorsId: json["vendors_id"] == null
            ? []
            : List<int>.from(json["vendors_id"].map((x) => x)),
        deliveryDate: DateTime.parse(json["delivery_date"]),
        prchOrdrSplitId: json["prch_ordr_split_id"],
      );

  // Map<String, dynamic> toJson() => {
  //       "customer_id": customerId,
  //       "inserted_customer_id": insertedCustomerId,
  //       "inserted_vendor_id": insertedVendorId,
  //       "purchase_order_id": purchaseOrderId,
  //       "orderGrpId": orderGrpId,
  //       "vendors_id": vendorsId == null
  //           ? []
  //           : List<dynamic>.from(vendorsId!.map((x) => x)),
  //       "deliveryDate": deliveryDate.toIso8601String(),
  //     };

  Map<String, dynamic> toJson() {
    final DateFormat formatter = DateFormat('yyyy-MM-dd HH:mm:ss');
    String formattedDeliveryDate = deliveryDate != null
        ? formatter.format(deliveryDate)
        : ''; // Handle null case

    return {
      "customer_id": customerId,
      "inserted_customer_id": insertedCustomerId,
      "inserted_vendor_id": insertedVendorId,
      "purchase_order_id": purchaseOrderId,
      "orderGrpId": orderGrpId,
      "vendors_id":
          vendorsId == null ? [] : List<dynamic>.from(vendorsId!.map((x) => x)),
      "deliveryDate": formattedDeliveryDate, // Use the formatted date
      "prch_ordr_split_id": prchOrdrSplitId,
    };
  }
}
