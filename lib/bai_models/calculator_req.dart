import 'dart:convert';

class CalculatorReq {
  List<int>? offersId;
  num? transportationCharges;
  num? discount;

  CalculatorReq({
    this.offersId,
    this.transportationCharges,
    this.discount,
  });

  factory CalculatorReq.fromRawJson(String str) =>
      CalculatorReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CalculatorReq.fromJson(Map<String, dynamic> json) => CalculatorReq(
        offersId: json["offersId"] == null
            ? []
            : List<int>.from(json["offersId"]!.map((x) => x)),
        transportationCharges: json["transportationCharges"],
        discount: json["discount"],
      );

  Map<String, dynamic> toJson() => {
        "offersId":
            offersId == null ? [] : List<dynamic>.from(offersId!.map((x) => x)),
        "transportationCharges": transportationCharges,
        "discount": discount,
      };
}
