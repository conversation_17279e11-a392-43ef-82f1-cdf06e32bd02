import 'dart:io';

import 'package:audio_session/audio_session.dart';
import 'package:connectone/bai_blocs/cubit/status_dropdown_cubit.dart';
import 'package:connectone/bai_blocs/offers/cubit/offers_cubit.dart';
import 'package:connectone/bai_models/bai_products_res.dart' as bpr;
import 'package:connectone/bai_models/change_status_req.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_blocs/offline_stocks/offline_stocks_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:flutter_sound_platform_interface/flutter_sound_recorder_platform_interface.dart';
import 'package:image_picker/image_picker.dart';
import 'package:location/location.dart' as l;
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/foundation.dart';

// ignore: must_be_immutable
class ChangeStatusDialog extends StatefulWidget {
  ChangeStatusDialog({
    Key? key,
    required this.vendorId,
    required this.enableAudio,
    required this.enablePictures,
    required this.enableComments,
    required this.enableLocation,
    required this.content,
    required this.reload,
    required this.statusCd,
    required this.statusName,
    required this.prchOrdrId,
    required this.assignedVendorId,
    this.vendorEmail,
    this.adminEmail,
  }) : super(key: key);

  final num? vendorId;
  final bool enableAudio;
  final bool enablePictures;
  final bool enableComments;
  final bool enableLocation;
  final bpr.Content content;
  final Function reload;
  final String statusCd;
  final String statusName;
  List<int> prchOrdrId;
  int? assignedVendorId;
  String? vendorEmail;
  String? adminEmail;

  @override
  State<ChangeStatusDialog> createState() => _ChangeStatusDialogState();
}

class _ChangeStatusDialogState extends State<ChangeStatusDialog> {
  final List<String> _images = [];
  final List<Uint8List> _imageBytes = [];
  final List<String> _files = [];
  final List<Uint8List> _fileBytes = [];
  final List<String> _audios = [];
  final l.Location _location = l.Location();

  final ImagePicker picker = ImagePicker();

  l.LocationData? _currentPosition;

  Codec _codec = Codec.aacMP4;
  final String _mPath = 'audio_${DateTime.now().millisecondsSinceEpoch}.mp4';
  FlutterSoundPlayer? _mPlayer = FlutterSoundPlayer();
  FlutterSoundRecorder? _mRecorder = FlutterSoundRecorder();
  var theSource = AudioSource.microphone;
  bool _mPlayerIsInited = false;
  bool _mRecorderIsInited = false;
  bool _mPlaybackReady = false;

  final TextEditingController _notesController = TextEditingController();

  void showImageSourceDialog(BuildContext context) {
    getImage(fromCamera: true);
  }

  Future<void> getImage({bool fromCamera = true}) async {
    if (hasAttachmentsMaxed()) {
      alert(
          "You can attach a maximum of 5 files, including images, documents, and audio. Please remove some to add new ones.");
    } else {
      try {
        final XFile? image = await picker.pickImage(
          source: fromCamera ? ImageSource.camera : ImageSource.gallery,
          requestFullMetadata: false,
          imageQuality: 25,
        );
        if (image != null) {
          final Uint8List imageBytes = await image.readAsBytes();

          setState(() {
            _images.add(image.path);
            _imageBytes.add(imageBytes);
          });
        }
      } catch (e) {
        safePrint(e);
      }
    }
  }

  bool hasAttachmentsMaxed() {
    return _images.length + _files.length + _audios.length >= 5;
  }

  Future<void> openTheRecorder() async {
    if (!kIsWeb) {
      var status = await Permission.microphone.request();
      if (status != PermissionStatus.granted) {
        throw RecordingPermissionException(
            'Microphone permission not granted.');
      }
    }
    await _mRecorder!.openRecorder();
    if (!await _mRecorder!.isEncoderSupported(_codec) && kIsWeb) {
      _codec = Codec.opusWebM;
      if (!await _mRecorder!.isEncoderSupported(_codec) && kIsWeb) {
        _mRecorderIsInited = true;
        return;
      }
    }
    final session = await AudioSession.instance;
    await session.configure(AudioSessionConfiguration(
      avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
      avAudioSessionCategoryOptions:
          AVAudioSessionCategoryOptions.allowBluetooth |
              AVAudioSessionCategoryOptions.defaultToSpeaker,
      avAudioSessionMode: AVAudioSessionMode.spokenAudio,
      avAudioSessionRouteSharingPolicy:
          AVAudioSessionRouteSharingPolicy.defaultPolicy,
      avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
      androidAudioAttributes: const AndroidAudioAttributes(
        contentType: AndroidAudioContentType.speech,
        flags: AndroidAudioFlags.none,
        usage: AndroidAudioUsage.voiceCommunication,
      ),
      androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
      androidWillPauseWhenDucked: true,
    ));

    _mRecorderIsInited = true;
  }

  @override
  void initState() {
    super.initState();

    _mPlayer!.openPlayer().then((value) {
      setState(() {
        _mPlayerIsInited = true;
      });
    });

    openTheRecorder().then((value) {
      setState(() {
        _mRecorderIsInited = true;
      });
    });

    loc();
  }

  @override
  void dispose() {
    _mPlayer!.closePlayer();
    _mPlayer = null;
    _mRecorder!.closeRecorder();
    _mRecorder = null;
    super.dispose();
  }

  Future<void> loc() async {
    try {
      final currentLocation = await _getCurrentLocation();
      if (currentLocation != null) {
        if (!mounted) return;
        setState(() {
          _currentPosition = currentLocation;
        });
      }
    } catch (e) {
      safePrint(e);
    }
  }

  void record() {
    _mRecorder!
        .startRecorder(
      toFile: _mPath,
      codec: _codec,
      audioSource: theSource,
    )
        .then((value) {
      setState(() {});
    });
  }

  void stopRecorder() async {
    await _mRecorder!.stopRecorder().then((value) {
      setState(() {
        if (hasAttachmentsMaxed()) {
          _audios.clear();
          _mPlaybackReady = true;
          alert(
              "You can attach a maximum of 5 files, including images, documents, and audio. Please remove some to add new ones.");
        } else {
          _audios.clear();
          if (value != null) {
            _audios.add(value);
          }
          _mPlaybackReady = true;
        }
      });
    });
  }

  void play() {
    assert(_mPlayerIsInited &&
        _mPlaybackReady &&
        _mRecorder!.isStopped &&
        _mPlayer!.isStopped);
    _mPlayer!
        .startPlayer(
            fromURI: _mPath,
            whenFinished: () {
              setState(() {});
            })
        .then((value) {
      setState(() {});
    });
  }

  void stopPlayer() {
    _mPlayer!.stopPlayer().then((value) {
      setState(() {});
    });
  }

  getRecorderFn() {
    if (!_mRecorderIsInited || !_mPlayer!.isStopped) {
      return null;
    }

    return _mRecorder!.isStopped ? record() : stopRecorder();
  }

  getPlaybackFn() {
    if (!_mPlayerIsInited || !_mPlaybackReady || !_mRecorder!.isStopped) {
      return null;
    }

    return _mPlayer!.isStopped ? play() : stopPlayer();
  }

  Future<l.LocationData?> _getCurrentLocation() async {
    var serviceEnabled = await _location.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await _location.requestService();
      if (!serviceEnabled) {
        alert('Location service not enabled!');
        return null;
      }
    }
    final permissionStatus = await _location.hasPermission();

    if (permissionStatus != l.PermissionStatus.granted) {
      final requestStatus = await _location.requestPermission();
      if (requestStatus != l.PermissionStatus.granted) {
        alert('Location permission denied');
        return null;
      }
    }

    return await _location.getLocation();
  }

  @override

  /// Shows a dialog with a form to change the status of a material request.
  /// The form allows the user to select a new status, add notes, record an audio
  /// note, take a picture, add a location, and submit the form.
  ///
  /// If the user presses the back button, the dialog is closed.
  ///
  /// The dialog is not closable by the user.
  ///
  /// The dialog is displayed in the center of the screen.
  ///
  /// The height of the dialog is determined by the height of the screen.
  ///
  /// The width of the dialog is determined by the width of the screen minus 88.
  ///
  /// The dialog is decorated with a white background color, black label text,
  /// and black text. The border is 0.75px wide. The label text is not dense.
  ///
  /// The [StatusDropdownCubit] is used to handle the form submission.
  /// When the form is submitted, the [StatusDropdownCubit] is called with the
  /// form data. The [StatusDropdownCubit] then calls the [ChangeStatusReq]
  /// constructor with the form data and sends the request to the server.
  /// When the server responds, the [StatusDropdownCubit] handles the response
  /// and shows an alert with the response message. If the response is successful,
  /// the [StatusDropdownCubit] calls the [reload] callback to reload the data.
  ///
  /// The [reload] callback is called when the user presses the back button.
  ///
  /// The [StatusDropdownCubit] is used to handle the form submission.
  /// When the form is submitted, the [StatusDropdownCubit] is called with the
  /// form data. The [StatusDropdownCubit] then calls the [ChangeStatusReq]
  /// constructor with the form data and sends the request to the server.
  /// When the server responds, the [StatusDropdownCubit] handles the response
  /// and shows an alert with the response message. If the response is successful,
  /// the [StatusDropdownCubit] calls the [reload] callback to reload the data.
  Widget build(BuildContext context) {
    return BlocConsumer<StatusDropdownCubit, StatusDropdownState>(
      listener: (context, state) {
        if (state is StatusChangeSuccess) {
          alert(state.message);
          widget.reload();
          context.read<OffersCubit>().refreshBuyerPage();
          context.read<OffersCubit>().refreshSellerPage();
          if (widget.statusCd == "DLTD") {
            context.read<OfflineMainBloc>().add(const RefreshOfflineStocks());
          }
          Navigator.pop(context);
        }
        if (state is StatusChangeFailed) {
          alert(state.message);
        }
      },
      builder: (context, state) {
        var size = MediaQuery.of(context).size;
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          insetPadding: const EdgeInsets.all(8),
          child: Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
            ),
            child: state is StatusDropdownLoading
                ? SizedBox(
                    height: size.height * 0.4,
                    width: kIsWeb ? 300 : size.width * 0.5,
                    child: const Center(child: CircularProgressIndicator()))
                : SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          height: 52,
                          decoration: BoxDecoration(
                            color: AppColors.primaryColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.handshake_outlined,
                                  color: AppColors.white,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  widget.statusName,
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ).withCloseButton(() => Navigator.pop(context)),
                        const SizedBox(height: 24),
                        if (widget.statusCd == "POBY")
                          Padding(
                            padding: const EdgeInsets.only(top: 24),
                            child: Text(
                              "Please email the reviewed PO to the vendor's email address ${widget.vendorEmail}.",
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        if (widget.statusCd == "PLCD")
                          Padding(
                            padding: const EdgeInsets.only(
                              top: 16,
                              bottom: 20,
                            ),
                            child: Text(
                              "Copy of email is sent to the admin's email address ${widget.adminEmail} for review.",
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        if (widget.enableComments) const SizedBox(height: 16),
                        if (widget.enableComments)
                          const Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "Add Notes",
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ),
                        if (widget.enableComments) const SizedBox(height: 16),
                        if (widget.enableComments)
                          TextField(
                            maxLines: 4,
                            controller: _notesController,
                            decoration: InputDecoration(
                              hintText: 'Add Notes',
                              // filled: true,
                              // fillColor: Colors.grey.shade300,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(4),
                                borderSide:
                                    const BorderSide(color: Colors.black),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(4),
                                borderSide:
                                    const BorderSide(color: Colors.black),
                              ),
                            ),
                          ),
                        const SizedBox(height: 16),
                        if (widget.enableAudio)
                          const Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "Add Voice Note",
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ),
                        if (widget.enableAudio) const SizedBox(height: 16),
                        if (widget.enableAudio)
                          Container(
                            // height: 56,
                            decoration: BoxDecoration(
                              // color: Colors.grey.shade300,
                              border: Border.all(color: Colors.black),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                          child: Text(_audios.isNotEmpty
                                              ? "Voice Note Added"
                                              : "Add Voice Note")),
                                      const Icon(Icons.mic),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Row(children: [
                                    SizedBox(
                                      width: kIsWeb
                                          ? 100
                                          : MediaQuery.of(context).size.width *
                                              0.25,
                                      child: ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                AppColors.primaryColor),
                                        onPressed: () {
                                          getRecorderFn();
                                        },
                                        child: Text(_mRecorder!.isRecording
                                            ? 'Stop'
                                            : 'Record'),
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 20,
                                    ),
                                    Text(_mRecorder!.isRecording
                                        ? 'Recording in progress'
                                        : 'Recorder is stopped'),
                                  ]),
                                ],
                              ),
                            ),
                          ),
                        const SizedBox(height: 20),
                        if (_audios.isNotEmpty)
                          Container(
                            padding: const EdgeInsets.all(8),
                            height: 80,
                            width: double.infinity,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              // color: Colors.grey.shade300,
                              border: Border.all(color: Colors.black),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Row(children: [
                              SizedBox(
                                width: kIsWeb
                                    ? 100
                                    : MediaQuery.of(context).size.width * 0.25,
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.primaryColor),
                                  onPressed: () {
                                    getPlaybackFn();
                                  },
                                  child: Text(
                                      _mPlayer!.isPlaying ? 'Stop' : 'Play'),
                                ),
                              ),
                              const SizedBox(
                                width: 20,
                              ),
                              Text(_mPlayer!.isPlaying
                                  ? 'Playback in progress'
                                  : 'Player is stopped'),
                              const Spacer(),
                              IconButton(
                                onPressed: () {
                                  setState(() {
                                    _audios.clear();
                                  });
                                },
                                icon: const Icon(
                                  Icons.delete,
                                  color: Colors.red,
                                ),
                              ),
                            ]),
                          ),
                        if (widget.enableAudio) const SizedBox(height: 16),
                        if (widget.enablePictures)
                          Container(
                            decoration: BoxDecoration(
                              // color: Colors.grey.shade300,
                              border: Border.all(color: Colors.black),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      if (_images.isNotEmpty)
                                        Flexible(
                                          child: SizedBox(
                                            height: 60,
                                            child: ListView.separated(
                                              scrollDirection: Axis.horizontal,
                                              itemCount: _images.length,
                                              padding: const EdgeInsets.all(6),
                                              itemBuilder: (context, index) {
                                                return Stack(
                                                  clipBehavior: Clip.none,
                                                  children: [
                                                    Container(
                                                      decoration: BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8)),
                                                      clipBehavior:
                                                          Clip.hardEdge,
                                                      child: kIsWeb
                                                          ? Image.network(
                                                              _images[index],
                                                              width: 64,
                                                              height: 64,
                                                              fit: BoxFit.cover,
                                                            )
                                                          : Image.file(
                                                              File(_images[
                                                                  index]),
                                                              width: 64,
                                                              height: 64,
                                                              fit: BoxFit.cover,
                                                            ),
                                                    ),
                                                    Positioned(
                                                      top: -8,
                                                      right: -8,
                                                      child: InkWell(
                                                        onTap: () {
                                                          setState(() {
                                                            _images.removeAt(
                                                                index);
                                                            _imageBytes
                                                                .removeAt(
                                                                    index);
                                                          });
                                                        },
                                                        child: Container(
                                                          decoration:
                                                              BoxDecoration(
                                                            border: Border.all(
                                                                color:
                                                                    Colors.red),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        16),
                                                            color: Colors
                                                                .transparent,
                                                          ),
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(2),
                                                          child: const Icon(
                                                            Icons.close,
                                                            size: 16,
                                                            color: Colors.red,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                );
                                              },
                                              separatorBuilder:
                                                  (BuildContext context,
                                                      int index) {
                                                return const SizedBox(
                                                    width: 12);
                                              },
                                            ),
                                          ),
                                        ),
                                      if (_images.isEmpty)
                                        const Text('Add Image'),
                                      const Spacer(),
                                      Container(
                                        padding: const EdgeInsets.all(2),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(33),
                                          color: Colors.black,
                                        ),
                                        child: IconButton(
                                          icon: const Icon(
                                            Icons.photo,
                                            color: AppColors.white,
                                          ),
                                          onPressed: () {
                                            setState(() {
                                              showImageSourceDialog(context);
                                            });
                                          },
                                        ),
                                      )
                                    ],
                                  )
                                ],
                              ),
                            ),
                          ),
                        if (widget.enablePictures) const SizedBox(height: 16),
                        // if (widget.enableLocation)
                        //   const Align(
                        //     alignment: Alignment.centerLeft,
                        //     child: Text(
                        //       "Location",
                        //       style: TextStyle(fontWeight: FontWeight.bold),
                        //     ),
                        //   ),
                        // if (widget.enableLocation) const SizedBox(height: 16),
                        // if (widget.enableLocation)
                        //   Container(
                        //     decoration: BoxDecoration(
                        //       color: Colors.grey.shade300,
                        //       border: Border.all(color: Colors.black),
                        //       borderRadius: BorderRadius.circular(4),
                        //     ),
                        //     child: Padding(
                        //       padding: const EdgeInsets.all(8.0),
                        //       child: Column(
                        //         crossAxisAlignment: CrossAxisAlignment.start,
                        //         children: [
                        //           Container(
                        //             alignment: Alignment.centerLeft,
                        //             child: Text(
                        //               "${_currentPosition?.latitude ?? "N/A"}, ${_currentPosition?.longitude ?? "N/A"}",
                        //               style: const TextStyle(
                        //                   fontWeight: FontWeight.bold),
                        //             ),
                        //           ),
                        //         ],
                        //       ),
                        //     ),
                        //   ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: SizedBox(
                                height: 40,
                                child: ElevatedButton(
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                  ),
                                  child: const Text(
                                    'CLOSE',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: SizedBox(
                                height: 40,
                                child: ElevatedButton(
                                  onPressed: () async {
                                    // if (widget.enableAudio) {
                                    //   if (_audios.isEmpty) {
                                    //     alert("Please attach audio.");
                                    //     return;
                                    //   }
                                    // }

                                    // if (widget.enablePictures) {
                                    //   if (_images.isEmpty) {
                                    //     alert("Please attach image.");
                                    //     return;
                                    //   }
                                    // }

                                    // if (widget.enableComments) {
                                    //   if (_notesController.text.isEmpty) {
                                    //     alert("Please add notes.");
                                    //     return;
                                    //   }
                                    // }

                                    // if (widget.enableLocation) {
                                    //   if (_currentPosition == null) {
                                    //     alert("Please add location.");
                                    //     return;
                                    //   }
                                    // }

                                    var req = ChangeStatusReq(
                                      prchOrdrId: widget.prchOrdrId,
                                      statusCd: widget.statusCd,
                                      assignedVendorsId:
                                          widget.assignedVendorId,
                                      locationLat:
                                          _currentPosition?.latitude.toString(),
                                      locationLong: _currentPosition?.longitude
                                          .toString(),
                                      comments: _notesController.text,
                                      orderGrpId:
                                          widget.content.orderGroupId?.toInt(),
                                    );

                                    context
                                        .read<StatusDropdownCubit>()
                                        .changeStatusOfMR(
                                          req,
                                          _images + _audios,
                                          _imageBytes,
                                        );
                                    // Navigator.pop(context);
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.primaryColor,
                                  ),
                                  child: const Text(
                                    'SUBMIT',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
          ),
        );
      },
    );
  }
}
