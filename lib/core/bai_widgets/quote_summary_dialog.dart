import 'package:connectone/bai_blocs/offers/cubit/offers_cubit.dart';
import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/bai_models/summary_res.dart';
import 'package:connectone/core/bai_widgets/assign_dialog.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/bai_widgets/quote_summary_widgets.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class QuoteSummaryDialog extends StatefulWidget {
  const QuoteSummaryDialog({
    required this.prchOrdrId,
    required this.categoryId,
    required this.content,
    required this.date,
    required this.steelMr,
    Key? key,
  }) : super(key: key);

  final int prchOrdrId;
  final String? categoryId;
  final Content content;
  final DateTime date;
  final bool steelMr;

  @override
  State<QuoteSummaryDialog> createState() => _QuoteSummaryDialogState();
}

class _QuoteSummaryDialogState extends State<QuoteSummaryDialog> {
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadQuoteSummary();
  }

  void _loadQuoteSummary() {
    setState(() {
      isLoading = true;
    });

    context.read<OffersCubit>().loadQuoteSummary(
          widget.prchOrdrId,
          widget.categoryId ??
              widget.content.cappCategoriesId?.toString() ??
              "",
          date: widget.date,
          prchOrdrSplitId: widget.categoryId != null
              ? int.tryParse(widget.categoryId!)
              : null,
          steelMr: widget.steelMr,
        );
  }

  List<SummaryResponse> summaryList = [];

  @override

  /// Builds the widget tree for the quote summary dialog.
  ///
  /// Displays a [Dialog] with the following children:
  ///
  /// * A header with the title "Quote Summary".
  /// * A [Row] with an [Icon] and a [Text] that displays the title.
  /// * A [SizedBox] with a height of 16.
  /// * A [Column] with the following children:
  ///   - If [isLoading] is true, a [Center] with a [CircularProgressIndicator].
  ///   - If [isLoading] is false, an [Expanded] widget with a [ListView] of
  ///     [SummaryTable] widgets or a [Center] with a [Text] that displays "No
  ///     summary found".
  /// * A [SizedBox] with a height of 16.
  /// * A [BaiButton] with the title "INVITE VENDORS" that navigates to the
  ///   assign dialog when tapped.
  Widget build(BuildContext context) {
    return BlocConsumer<OffersCubit, OffersState>(
      listener: (context, state) {
        if (state is QuoteSummaryLoaded) {
          setState(() {
            isLoading = false;
            summaryList = state.summary;
          });
        } else if (state is QuoteSummaryLoading) {
          setState(() {
            isLoading = true;
          });
        }
      },
      builder: (context, state) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          insetPadding: const EdgeInsets.all(8),
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  height: 52,
                  decoration: BoxDecoration(
                    color: AppColors.primaryColorOld,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.summarize, color: Colors.white, size: 28),
                      SizedBox(width: 8),
                      Text(
                        'Quote Summary',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ).withCloseButton(() => Navigator.pop(context)),
                const SizedBox(height: 16),
                if (isLoading)
                  const Center(
                    child: SizedBox(
                      height: 80,
                      width: 80,
                      child: Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                  )
                else
                  Expanded(
                    child: summaryList.isNotEmpty
                        ? Scrollbar(
                            child: ListView.separated(
                              itemCount: summaryList.length,
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.all(0),
                                  child: SummaryTable(
                                    res: summaryList,
                                    index: index,
                                    fromAssign: true,
                                    content: widget.content,
                                    statusCd: widget.content.statusCd ?? "",
                                    date: widget.date,
                                    categoryId: widget.categoryId,
                                    steelMr: widget.steelMr,
                                  ),
                                );
                              },
                              separatorBuilder: (context, index) {
                                return Column(
                                  children: [
                                    const SizedBox(height: 16),
                                    Row(
                                      children: [
                                        const SizedBox(width: 6),
                                        Text(
                                          "End of Summary ${index + 1}",
                                          style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.black,
                                          ),
                                        ),
                                        const Expanded(
                                          child: Divider(
                                            height: 20,
                                            color: Colors.black54,
                                            thickness: 2,
                                            indent: 8,
                                            endIndent: 6,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 20),
                                  ],
                                );
                              },
                            ),
                          )
                        : const Center(
                            child: Text("No summary found"),
                          ),
                  ),
                const SizedBox(height: 16),
                if (!isLoading)
                  BaiButton(
                    onTap: () {
                      if ((widget.content.cappCategoriesId ?? 0) < 0) {
                        properAlert(
                            "Can not assign vendors to General category MR. \n\nPlease assign a category to the MR.");
                        return;
                      }
                      Navigator.pop(context);
                      showDialog(
                          context: context,
                          builder: (context) {
                            return AssignDialog(
                              content: widget.content,
                              reload: () {},
                              changeStatus: () {},
                              categoryId: widget.categoryId,
                              date: widget.date,
                            );
                          });
                    },
                    text: "INVITE VENDORS",
                    height: 48,
                    backgoundColor: Colors.green.shade800,
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
