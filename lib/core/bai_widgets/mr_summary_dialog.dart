// import 'package:connectone/bai_blocs/mr_summary/cubit/mr_summary_cubit.dart';
// import 'package:connectone/bai_models/delivery_dates_res.dart';
// import 'package:connectone/core/utils/colors.dart';
// import 'package:connectone/core/utils/extensions.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';

// class MRSummaryDialog extends StatefulWidget {
//   final String orderGroupId;
//   const MRSummaryDialog({
//     Key? key,
//     required this.orderGroupId,
//   }) : super(key: key);

//   @override
//   State<MRSummaryDialog> createState() => _MRSummaryDialogState();
// }

// class _MRSummaryDialogState extends State<MRSummaryDialog> {
//   final List<Map<String, String>> mrData = [
//     {'date': '1/April.2025', 'mrs': '10', 'action': 'Open'},
//     {'date': '10/April.2025', 'mrs': '3', 'action': 'Open'},
//     {'date': '15/April.2025', 'mrs': '2', 'action': 'Open'},
//   ];

//   @override
//   void initState() {
//     super.initState();
//     context.read<MrSummaryCubit>().getDeliveryDates(widget.orderGroupId);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Dialog(
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(16),
//       ),
//       insetPadding: const EdgeInsets.all(8),
//       child: BlocConsumer<MrSummaryCubit, MrSummaryState>(
//         listener: (context, state) {
//           if (state is MrSummaryLoaded) {
//             if (state.deliveryDates.data?.showPopUp == false) {
//               Navigator.pop(
//                 context,
//                 [
//                   state.deliveryDates.data?.deliveryDates?[0].deliveryDate,
//                   state.deliveryDates.data?.deliveryDates?[0].categoryId,
//                 ],
//               );
//             }
//           }
//         },
//         builder: (context, state) {
//           if (state is MrSummaryLoading) {
//             return const SizedBox();
//             // return Container(
//             //   padding: const EdgeInsets.all(20),
//             //   margin: const EdgeInsets.all(20),
//             //   height: 300,
//             //   color: Colors.transparent,
//             //   child: const Center(
//             //     child: CircularProgressIndicator(),
//             //   ),
//             // );
//           } else if (state is MrSummaryError) {
//             return Container(
//               padding: const EdgeInsets.all(20),
//               margin: const EdgeInsets.all(20),
//               height: 300,
//               child: Center(
//                 child: Text(
//                   state.error,
//                   textAlign: TextAlign.center,
//                 ),
//               ),
//             );
//           }
//           DeliveryDatesRes deliveryDates = (state is MrSummaryLoaded)
//               ? state.deliveryDates
//               : DeliveryDatesRes();
//           if (deliveryDates.data?.showPopUp == false) {
//             return const SizedBox();
//           }
//           return Container(
//             padding: const EdgeInsets.all(8),
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(16),
//               color: Colors.white,
//               boxShadow: const [
//                 BoxShadow(
//                   color: Colors.black26,
//                   blurRadius: 10,
//                   offset: Offset(0, 4),
//                 ),
//               ],
//             ),
//             child: Column(
//               mainAxisSize: MainAxisSize.min,
//               crossAxisAlignment: CrossAxisAlignment.stretch,
//               children: [
//                 Container(
//                   padding: const EdgeInsets.all(8),
//                   decoration: BoxDecoration(
//                     color: AppColors.primaryColorOld,
//                     borderRadius: BorderRadius.circular(8),
//                   ),
//                   height: 52,
//                   child: const Row(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       Text(
//                         'Select Delivery Date',
//                         style: TextStyle(
//                           fontSize: 16,
//                           fontWeight: FontWeight.bold,
//                           color: Colors.white,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ).withCloseButton(() => Navigator.pop(context)),
//                 const SizedBox(height: 16),
//                 Text(
//                   'Site: ${deliveryDates.data?.projectName}',
//                   textAlign: TextAlign.center,
//                   style: const TextStyle(
//                     fontSize: 16,
//                     color: Colors.black,
//                   ),
//                 ),
//                 const SizedBox(height: 16),
//                 Container(
//                   decoration: BoxDecoration(
//                     border: Border.all(color: Colors.grey.shade300),
//                     borderRadius: BorderRadius.circular(8),
//                   ),
//                   child: ListView.separated(
//                     shrinkWrap: true,
//                     physics: const NeverScrollableScrollPhysics(),
//                     itemCount:
//                         (deliveryDates.data?.deliveryDates?.length ?? 0) + 1,
//                     separatorBuilder: (context, index) => index == 0
//                         ? Divider(height: 1, color: Colors.grey.shade300)
//                         : Divider(height: 1, color: Colors.grey.shade200),
//                     itemBuilder: (context, index) {
//                       if (index == 0) {
//                         return _buildHeaderRow();
//                       }
//                       final data =
//                           deliveryDates.data!.deliveryDates![index - 1];
//                       return _buildDataRow(
//                           data.deliveryDate?.toDeliveryOn() ?? "",
//                           data.categoryName.toString(),
//                           data.mrCount.toString(),
//                           "", () {
//                         Navigator.pop(
//                             context, [data.deliveryDate, data.categoryId]);
//                       });
//                     },
//                   ),
//                 ),
//                 // const SizedBox(height: 4),
//                 // BaiButton(
//                 //   onTap: () {
//                 //     Navigator.pop(context);
//                 //   },
//                 //   text: "OK",
//                 //   borderRadius: 8,
//                 //   backgoundColor: AppColors.primaryColorOld,
//                 // ),
//               ],
//             ),
//           );
//         },
//       ),
//     );
//   }

//   Widget _buildHeaderRow() {
//     return Container(
//       decoration: const BoxDecoration(
//         borderRadius: BorderRadius.only(
//           topLeft: Radius.circular(8),
//           topRight: Radius.circular(8),
//         ),
//         color: AppColors.primaryColorOld,
//       ),
//       padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
//       child: const Row(
//         children: [
//           Expanded(
//             flex: 4,
//             child: Text(
//               'Category',
//               style: TextStyle(
//                 fontWeight: FontWeight.bold,
//                 color: Colors.white,
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ),
//           Expanded(
//             flex: 4,
//             child: Text(
//               'Delivery DT',
//               style: TextStyle(
//                 fontWeight: FontWeight.bold,
//                 color: Colors.white,
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ),
//           Expanded(
//             flex: 2,
//             child: Text(
//               'MRs',
//               style: TextStyle(
//                 fontWeight: FontWeight.bold,
//                 color: Colors.white,
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ),
//           Expanded(
//             flex: 3,
//             child: Text(
//               'Action',
//               style: TextStyle(
//                 fontWeight: FontWeight.bold,
//                 color: Colors.white,
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildDataRow(
//     String date,
//     String categoryName,
//     String mrs,
//     String action,
//     VoidCallback onTap,
//   ) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
//       child: Row(
//         children: [
//           Expanded(
//             flex: 4,
//             child: Text(
//               categoryName,
//               style: const TextStyle(fontWeight: FontWeight.bold),
//               textAlign: TextAlign.center,
//               maxLines: 2,
//               overflow: TextOverflow.ellipsis,
//             ),
//           ),
//           Expanded(
//             flex: 4,
//             child: Text(
//               date,
//               style: const TextStyle(fontWeight: FontWeight.bold),
//               textAlign: TextAlign.center,
//             ),
//           ),
//           Expanded(
//             flex: 2,
//             child: Text(
//               mrs,
//               style: const TextStyle(),
//               textAlign: TextAlign.center,
//             ),
//           ),
//           Expanded(
//             flex: 3,
//             child: SizedBox(
//               height: 30,
//               child: ElevatedButton(
//                 onPressed: () {
//                   onTap();
//                 },
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: AppColors.primaryColorOld,
//                   padding: const EdgeInsets.symmetric(horizontal: 0),
//                   shape: RoundedRectangleBorder(
//                     borderRadius: BorderRadius.circular(4),
//                   ),
//                 ),
//                 child: const Text(
//                   'Select',
//                   style: TextStyle(
//                     color: Colors.white,
//                     fontSize: 12,
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

// import 'package:connectone/bai_blocs/mr_summary/cubit/mr_summary_cubit.dart';
// import 'package:connectone/bai_models/delivery_dates_res.dart';
// import 'package:connectone/core/utils/colors.dart';
// import 'package:connectone/core/utils/extensions.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';

// class MRSummaryDialog extends StatefulWidget {
//   final String orderGroupId;
//   const MRSummaryDialog({
//     Key? key,
//     required this.orderGroupId,
//   }) : super(key: key);

//   @override
//   State<MRSummaryDialog> createState() => _MRSummaryDialogState();
// }

// class _MRSummaryDialogState extends State<MRSummaryDialog> {
//   final List<Map<String, String>> mrData = [
//     {'date': '1/April.2025', 'mrs': '10', 'action': 'Open'},
//     {'date': '10/April.2025', 'mrs': '3', 'action': 'Open'},
//     {'date': '15/April.2025', 'mrs': '2', 'action': 'Open'},
//   ];

//   @override
//   void initState() {
//     super.initState();
//     context.read<MrSummaryCubit>().getDeliveryDates(widget.orderGroupId);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Dialog(
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(16),
//       ),
//       insetPadding: const EdgeInsets.all(8),
//       child: BlocConsumer<MrSummaryCubit, MrSummaryState>(
//         listener: (context, state) {
//           if (state is MrSummaryLoaded) {
//             //  if state.deliveryDates.data?.deliveryDates?[0].splitId != 0 pass it else null
//             if (state.deliveryDates.data?.showPopUp == false) {
//               Navigator.pop(
//                 context,
//                 [
//                   state.deliveryDates.data?.deliveryDates?[0].deliveryDate,
//                   state.deliveryDates.data?.deliveryDates?[0].splitId,
//                   state.deliveryDates.data?.deliveryDates?[0].splitName,
//                   state.deliveryDates.data?.deliveryDates?[0].steelMr,
//                 ],
//               );
//             }
//           }
//         },
//         builder: (context, state) {
//           if (state is MrSummaryLoading) {
//             return const SizedBox();
//             // return Container(
//             //   padding: const EdgeInsets.all(20),
//             //   margin: const EdgeInsets.all(20),
//             //   height: 300,
//             //   color: Colors.transparent,
//             //   child: const Center(
//             //     child: CircularProgressIndicator(),
//             //   ),
//             // );
//           } else if (state is MrSummaryError) {
//             return Container(
//               padding: const EdgeInsets.all(20),
//               margin: const EdgeInsets.all(20),
//               height: 300,
//               child: Center(
//                 child: Text(
//                   state.error,
//                   textAlign: TextAlign.center,
//                 ),
//               ),
//             );
//           }
//           DeliveryDatesRes deliveryDates = (state is MrSummaryLoaded)
//               ? state.deliveryDates
//               : DeliveryDatesRes();
//           if (deliveryDates.data?.showPopUp == false) {
//             return const SizedBox();
//           }
//           return Container(
//             padding: const EdgeInsets.all(8),
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(16),
//               color: Colors.white,
//               boxShadow: const [
//                 BoxShadow(
//                   color: Colors.black26,
//                   blurRadius: 10,
//                   offset: Offset(0, 4),
//                 ),
//               ],
//             ),
//             child: Column(
//               mainAxisSize: MainAxisSize.min,
//               crossAxisAlignment: CrossAxisAlignment.stretch,
//               children: [
//                 Container(
//                   padding: const EdgeInsets.all(8),
//                   decoration: BoxDecoration(
//                     color: AppColors.primaryColorOld,
//                     borderRadius: BorderRadius.circular(8),
//                   ),
//                   height: 52,
//                   child: const Row(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       Text(
//                         'Select Split Group',
//                         style: TextStyle(
//                           fontSize: 16,
//                           fontWeight: FontWeight.bold,
//                           color: Colors.white,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ).withCloseButton(() => Navigator.pop(context)),
//                 const SizedBox(height: 16),
//                 Text(
//                   'Site: ${deliveryDates.data?.projectName}',
//                   textAlign: TextAlign.center,
//                   style: const TextStyle(
//                     fontSize: 16,
//                     color: Colors.black,
//                   ),
//                 ),
//                 const SizedBox(height: 16),
//                 Container(
//                   decoration: BoxDecoration(
//                     border: Border.all(color: Colors.grey.shade300),
//                     borderRadius: BorderRadius.circular(8),
//                   ),
//                   child: ListView.separated(
//                     shrinkWrap: true,
//                     // physics: const NeverScrollableScrollPhysics(),
//                     itemCount:
//                         (deliveryDates.data?.deliveryDates?.length ?? 0) + 1,
//                     separatorBuilder: (context, index) => index == 0
//                         ? Divider(height: 1, color: Colors.grey.shade300)
//                         : Divider(height: 1, color: Colors.grey.shade200),
//                     itemBuilder: (context, index) {
//                       if (index == 0) {
//                         return _buildHeaderRow();
//                       }
//                       final data =
//                           deliveryDates.data!.deliveryDates![index - 1];
//                       return _buildDataRow(
//                           data.deliveryDate?.toDeliveryOn() ?? "",
//                           data.splitName.toString(),
//                           data.mrCount.toString(),
//                           data.splitStatus?.toString() ?? "N/A", () {
//                         Navigator.pop(context, [
//                           data.deliveryDate,
//                           data.splitId,
//                           data.splitName,
//                           data.steelMr,
//                         ]);
//                       });
//                     },
//                   ),
//                 ),
//                 // const SizedBox(height: 4),
//                 // BaiButton(
//                 //   onTap: () {
//                 //     Navigator.pop(context);
//                 //   },
//                 //   text: "OK",
//                 //   borderRadius: 8,
//                 //   backgoundColor: AppColors.primaryColorOld,
//                 // ),
//               ],
//             ),
//           );
//         },
//       ),
//     );
//   }

//   Widget _buildHeaderRow() {
//     return Container(
//       decoration: const BoxDecoration(
//         borderRadius: BorderRadius.only(
//           topLeft: Radius.circular(8),
//           topRight: Radius.circular(8),
//         ),
//         color: AppColors.primaryColorOld,
//       ),
//       padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
//       child: const Row(
//         children: [
//           Expanded(
//             flex: 4,
//             child: Text(
//               'Split Name',
//               style: TextStyle(
//                 fontWeight: FontWeight.bold,
//                 color: Colors.white,
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ),
//           Expanded(
//             flex: 4,
//             child: Text(
//               'Delivery DT',
//               style: TextStyle(
//                 fontWeight: FontWeight.bold,
//                 color: Colors.white,
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ),
//           Expanded(
//             flex: 2,
//             child: Text(
//               'MRs',
//               style: TextStyle(
//                 fontWeight: FontWeight.bold,
//                 color: Colors.white,
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ),
//           Expanded(
//             flex: 3,
//             child: Text(
//               'Status',
//               style: TextStyle(
//                 fontWeight: FontWeight.bold,
//                 color: Colors.white,
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildDataRow(
//     String date,
//     String categoryName,
//     String mrs,
//     String action,
//     VoidCallback onTap,
//   ) {
//     return InkWell(
//       onTap: onTap,
//       borderRadius: BorderRadius.circular(4),
//       child: Padding(
//         padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
//         child: Row(
//           children: [
//             Expanded(
//               flex: 4,
//               child: Text(
//                 categoryName,
//                 style: const TextStyle(fontWeight: FontWeight.bold),
//                 textAlign: TextAlign.center,
//                 maxLines: 2,
//                 overflow: TextOverflow.ellipsis,
//               ),
//             ),
//             Expanded(
//               flex: 4,
//               child: Text(
//                 date,
//                 style: const TextStyle(fontWeight: FontWeight.bold),
//                 textAlign: TextAlign.center,
//               ),
//             ),
//             Expanded(
//               flex: 2,
//               child: Text(
//                 mrs,
//                 style: const TextStyle(),
//                 textAlign: TextAlign.center,
//               ),
//             ),
//             Expanded(
//               flex: 3,
//               child: Text(
//                 action,
//                 style: const TextStyle(),
//                 textAlign: TextAlign.center,
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

import 'package:connectone/bai_blocs/mr_summary/cubit/mr_summary_cubit.dart';
import 'package:connectone/bai_models/delivery_dates_res.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MRSummaryDialog extends StatefulWidget {
  final String orderGroupId;
  const MRSummaryDialog({
    Key? key,
    required this.orderGroupId,
  }) : super(key: key);

  @override
  State<MRSummaryDialog> createState() => _MRSummaryDialogState();
}

class _MRSummaryDialogState extends State<MRSummaryDialog> {
  @override
  void initState() {
    super.initState();
    context.read<MrSummaryCubit>().getDeliveryDates(widget.orderGroupId);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      insetPadding: const EdgeInsets.all(8),
      // Prevent the dialog itself from resizing when the keyboard appears, etc.
      // This also helps contain the SingleChildScrollView properly.
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BlocConsumer<MrSummaryCubit, MrSummaryState>(
          listener: (context, state) {
            if (state is MrSummaryLoaded) {
              if (state.deliveryDates.data?.showPopUp == false) {
                Navigator.pop(
                  context,
                  [
                    state.deliveryDates.data?.deliveryDates?[0].deliveryDate,
                    state.deliveryDates.data?.deliveryDates?[0].splitId,
                    state.deliveryDates.data?.deliveryDates?[0].splitName,
                    state.deliveryDates.data?.deliveryDates?[0].steelMr,
                  ],
                );
              }
            }
          },
          builder: (context, state) {
            if (state is MrSummaryLoading) {
              return Container(
                padding: const EdgeInsets.all(20),
                height: 300,
                color: Colors.transparent,
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              );
            } else if (state is MrSummaryError) {
              return Container(
                padding: const EdgeInsets.all(20),
                height: 300,
                child: Center(
                  child: Text(
                    state.error,
                    textAlign: TextAlign.center,
                  ),
                ),
              );
            }
            DeliveryDatesRes deliveryDates = (state is MrSummaryLoaded)
                ? state.deliveryDates
                : DeliveryDatesRes();
            if (deliveryDates.data?.showPopUp == false) {
              return const SizedBox();
            }

            // *** CHANGE 1: Wrap the content in a SingleChildScrollView ***
            // This allows the entire dialog body to scroll if the list is too long.
            return SingleChildScrollView(
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: const BoxDecoration(
                  // The borderRadius is now on the ClipRRect above
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black26,
                      blurRadius: 10,
                      offset: Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.primaryColorOld,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      height: 52,
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Select Split Group',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ).withCloseButton(() => Navigator.pop(context)),
                    const SizedBox(height: 16),
                    Text(
                      'Site: ${deliveryDates.data?.projectName}',
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ListView.separated(
                        shrinkWrap: true,
                        // *** CHANGE 2: Keep NeverScrollableScrollPhysics ***
                        // This is correct because the parent SingleChildScrollView is handling the scroll.
                        // We don't want two competing scroll views.
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount:
                            (deliveryDates.data?.deliveryDates?.length ?? 0) +
                                1,
                        separatorBuilder: (context, index) => index == 0
                            ? Divider(height: 1, color: Colors.grey.shade300)
                            : Divider(height: 1, color: Colors.grey.shade200),
                        itemBuilder: (context, index) {
                          if (index == 0) {
                            return _buildHeaderRow();
                          }
                          final data =
                              deliveryDates.data!.deliveryDates![index - 1];
                          return _buildDataRow(
                              data.deliveryDate?.toDeliveryOn() ?? "",
                              data.splitName.toString(),
                              data.mrCount.toString(),
                              data.splitStatus?.toString() ?? "N/A", () {
                            Navigator.pop(context, [
                              data.deliveryDate,
                              data.splitId,
                              data.splitName,
                              data.steelMr,
                            ]);
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildHeaderRow() {
    return Container(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
        color: AppColors.primaryColorOld,
      ),
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      child: const Row(
        children: [
          Expanded(
            flex: 4,
            child: Text(
              'Split Name',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 4,
            child: Text(
              'Delivery DT',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'MRs',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              'Status',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataRow(
    String date,
    String categoryName,
    String mrs,
    String action,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(4),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
        child: Row(
          children: [
            Expanded(
              flex: 4,
              child: Text(
                categoryName,
                style: const TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Expanded(
              flex: 4,
              child: Text(
                date,
                style: const TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
            Expanded(
              flex: 2,
              child: Text(
                mrs,
                style: const TextStyle(),
                textAlign: TextAlign.center,
              ),
            ),
            Expanded(
              flex: 3,
              child: Text(
                action,
                style: const TextStyle(),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
