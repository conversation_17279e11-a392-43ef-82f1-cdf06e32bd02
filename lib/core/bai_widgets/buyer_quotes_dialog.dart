import 'package:connectone/bai_blocs/quotes_only/cubit/quotes_only_cubit.dart';
import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/bai_models/offers_filter_res.dart';
import 'package:connectone/bai_models/offers_res.dart';
import 'package:connectone/bai_models/view_offer_req.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/bai_widgets/buyer_card.dart';
import 'package:connectone/core/bai_widgets/filter_popup.dart';
import 'package:connectone/core/bai_widgets/negotiate_dialog.dart';
import 'package:connectone/core/bai_widgets/negotiation_card.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/others.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:connectone/bai_blocs/offers/cubit/offers_cubit.dart';

import '../utils/tools.dart';

class BuyerQuotesDialog extends StatefulWidget {
  const BuyerQuotesDialog({
    required this.prchOrdrId,
    this.brand,
    this.index = 0,
    Key? key,
  }) : super(key: key);

  final int prchOrdrId;
  final String? brand;
  final int? index;

  @override
  State<BuyerQuotesDialog> createState() => _BuyerQuotesDialogState();
}

class _BuyerQuotesDialogState extends State<BuyerQuotesDialog> {
  bool isLoading = false;
  List<Offer> offersList = [];
  int _selectedIndex = 0;
  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();

  var req = ViewOfferReq(
    variant1OptionGroupName: [],
    variant1OptionNames: [],
    variant2OptionGroupName: [],
    variant2OptionNames: [],
    variant3OptionGroupName: [],
    variant3OptionNames: [],
  );

  OffersFilterRes filter = OffersFilterRes(
    variant1OptionGroupName: [],
    variant1OptionNames: [],
    variant2OptionGroupName: [],
    variant2OptionNames: [],
    variant3OptionGroupName: [],
    variant3OptionNames: [],
  );

  @override
  void initState() {
    super.initState();
    // We'll set the default tab after data is loaded
    _selectedIndex = widget.index ?? 0; // Temporarily set to Quotes tab
    _loadQuoteSummary();
  }

  void _loadQuoteSummary() {
    setState(() => isLoading = true);
    context.read<QuotesOnlyCubit>().loadQuotesBuyer(
          widget.prchOrdrId,
          req,
          brand: widget.brand,
        );
  }

  BaiProductsRes? bpr;

  bool showQuantity() {
    return !(bpr?.content?[0].mvtItemName ?? "")
        .toLowerCase()
        .contains("reinforcement");
  }

  bool hideProductName() {
    return false;
  }

  @override

  /// Builds the dialog for the buyer quotes.
  ///
  /// Displays a [RefreshIndicator] that contains a [Column] with the following
  /// children:
  ///
  /// * A header with the title "Quotes" and a close button.
  /// * A [Padding] with a [Column] that displays the product name and
  ///   quantity, if applicable.
  /// * A [SizedBox] with a height of 16.
  /// * A [RefreshIndicator] that displays a loading indicator if the quotes
  ///   are loading, otherwise displays a list of quotes.
  /// * A [SizedBox] with a height of 16.
  Widget build(BuildContext context) {
    return BlocListener<OffersCubit, OffersState>(
      listener: (context, offersState) {
        if (offersState is RefreshBuyerPage ||
            offersState is BuyerOffersAccepted ||
            offersState is BuyerOffersNegotiated ||
            offersState is DiscardSuccess ||
            offersState is AcceptNegotiateFailed ||
            offersState is NegotiationAccepted ||
            offersState is BuyerOffersLoaded ||
            offersState is BuyerOffersFailed) {
          _loadQuoteSummary();
        }

        if (offersState is NotifyBuyerSuccess) {
          alert(offersState.message);
          _loadQuoteSummary();
        }

        if (offersState is NotifyBuyerFailed) {
          alert("Failed to notify seller: ${offersState.message}");
          _loadQuoteSummary();
        }

        if (offersState is BuyerOffersLoading) {
          setState(() {
            isLoading = true;
          });
        }
      },
      child: BlocConsumer<QuotesOnlyCubit, QuotesOnlyState>(
        listener: (context, state) {
          if (state is BuyerQuotesLoaded) {
            setState(() {
              isLoading = false;
              bpr = state.bpr;
              filter = state.filter;
              offersList = state.buyerOffers.offers ?? [];
            });

            // Check if negotiations exist and set the tab accordingly
            // Only change tab if widget.index is null (not explicitly set)

            final negotiations = offersList
                .where((e) => e.negotiationHistory?.isNotEmpty == true)
                .toList();
            setState(() {
              // If negotiations exist, show negotiations tab, otherwise show quotes tab
              _selectedIndex = negotiations.isNotEmpty ? 1 : 0;
            });
          } else if (state is BuyerQuotesLoading) {
            setState(() => isLoading = true);
          }
        },
        builder: (context, state) {
          var statusCd = bpr?.content?[0].statusCd;
          var allowNego = ["NEGO", "QUOT"].contains(statusCd);
          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            insetPadding: const EdgeInsets.all(8),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
              ),
              child: RefreshIndicator(
                onRefresh: () {
                  context.read<QuotesOnlyCubit>().loadQuotesBuyer(
                        widget.prchOrdrId,
                        req,
                        brand: widget.brand,
                      );
                  return Future.value();
                },
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildHeader()
                        .withCloseButton(() => Navigator.pop(context)),
                    const SizedBox(height: 8),
                    if (!isLoading)
                      Padding(
                        padding: const EdgeInsets.only(
                          top: 8,
                          left: 16,
                          right: 16,
                          bottom: 0,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: hideProductName()
                                      ? const SizedBox()
                                      : Text(
                                          '${bpr?.content?[0].mvtItemName}',
                                          style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                          maxLines: 3,
                                        ),
                                ),
                                hideProductName()
                                    ? const SizedBox()
                                    : IconButton(
                                        constraints: const BoxConstraints(),
                                        padding: EdgeInsets.zero,
                                        icon: const Icon(
                                            Icons.filter_alt_outlined),
                                        onPressed: () {
                                          showDialog(
                                            context: context,
                                            builder: (context) {
                                              return FilterPopup(
                                                data: filter,
                                                viewOfferReq: req,
                                                onApply: (filterReq) {
                                                  req = filterReq;
                                                  context
                                                      .read<QuotesOnlyCubit>()
                                                      .loadQuotesBuyer(
                                                        widget.prchOrdrId,
                                                        filterReq,
                                                        brand: widget.brand,
                                                      );
                                                },
                                              );
                                            },
                                          );
                                        },
                                      ),
                              ],
                            ),
                            if (showQuantity())
                              hideProductName()
                                  ? const SizedBox()
                                  : const SizedBox(height: 4),
                            if (showQuantity())
                              hideProductName()
                                  ? const SizedBox()
                                  : Text(
                                      'Quantity: ${bpr?.content?[0].quantity?.toStringAsFixed(0)} ${bpr?.content?[0].optionName ?? ""}',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                          ],
                        ),
                      ),
                    // const SizedBox(height: 16),
                    Container(
                      height: 64,
                      color: AppColors.white,
                      child: Container(
                        margin: const EdgeInsets.fromLTRB(0, 16, 0, 8),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: [
                                TabItem(
                                  key: key1,
                                  text: "Quotes",
                                  index: 0,
                                  selectedColor: AppColors.primaryColorOld,
                                  unselectedColor: AppColors.white,
                                  selectedTextColor: AppColors.white,
                                  unselectedTextColor: Colors.black,
                                  selectedIndex: _selectedIndex,
                                  onTap: (index) {
                                    setState(() {
                                      _selectedIndex = index;
                                    });
                                  },
                                  fontSize: 14,
                                  width:
                                      MediaQuery.of(context).size.width / 2.2,
                                ),
                                TabItem(
                                  key: key2,
                                  text: "Negotiations",
                                  index: 1,
                                  selectedColor: AppColors.primaryColorOld,
                                  unselectedColor: AppColors.white,
                                  selectedTextColor: AppColors.white,
                                  unselectedTextColor: Colors.black,
                                  selectedIndex: _selectedIndex,
                                  onTap: (index) {
                                    setState(() {
                                      _selectedIndex = index;
                                    });
                                  },
                                  fontSize: 14,
                                  width:
                                      MediaQuery.of(context).size.width / 2.2,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    Stack(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(
                              bottom: _selectedIndex == 1 ? 60 : 0),
                          child: isLoading
                              ? _buildLoadingIndicator()
                              : _buildOfferList(allowNego),
                        ),
                        // Notify Seller button for negotiation tab
                        if (_selectedIndex == 1 &&
                            offersList
                                .where((e) =>
                                    e.negotiationHistory?.isNotEmpty == true)
                                .toList()
                                .isNotEmpty)
                          Positioned(
                            bottom: 8,
                            left: 8,
                            right: 8,
                            child: BaiButton(
                              onTap: () {
                                _notifySeller(context);
                              },
                              text: "NOTIFY SELLER",
                              backgoundColor: AppColors.primaryColorOld,
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 52,
      decoration: BoxDecoration(
        color: AppColors.primaryColorOld,
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.summarize,
            color: Colors.white,
            size: 28,
          ),
          SizedBox(width: 8),
          Text(
            'Quotes',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: SizedBox(
        height: 40,
        width: 40,
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildOfferList(bool allowNego) {
    final negotiations = offersList
        .where((e) => e.negotiationHistory?.isNotEmpty == true)
        .toList();
    final displayList = _selectedIndex == 0 ? offersList : negotiations;

    if (!displayList.isNotEmpty) {
      return SizedBox(
        height: MediaQuery.of(context).size.height / 1.5,
        child: Center(
          child: Text(
            _selectedIndex == 0 ? "No quotes found" : "No negotiations found",
            style: largeStyle,
          ),
        ),
      );
    }

    return SizedBox(
      height: MediaQuery.of(context).size.height / 1.8,
      child: ListView.separated(
        padding: const EdgeInsets.symmetric(
          horizontal: 2,
          vertical: 0,
        ),
        itemCount: displayList.length,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          final item = displayList[index];
          return _selectedIndex == 0
              ? BuyerCard(
                  offerDetails: item,
                  onAccept: (offer) {
                    // context.read<QuotesOnlyCubit>().acceptOffer(offer.id.toString());
                  },
                  onNegotiate: (offer) => allowNego
                      ? _openNegotiateDialog(offer)
                      : properAlert("Negotiation not allowed."),
                  showQuantity: showQuantity(),
                )
              : NegotiationCard(
                  offerDetails: item,
                  onAccept: (offer) {
                    // context.read<QuotesOnlyCubit>().acceptOffer();
                  },
                  onNegotiate: (offer) => allowNego
                      ? _openNegotiateDialog(offer)
                      : properAlert("Negotiation not allowed."),
                  showQuantity: showQuantity(),
                );
        },
        separatorBuilder: (context, index) => const SizedBox(height: 12),
      ),
    );
  }

  void _openNegotiateDialog(Offer offer) {
    showDialog(
      context: context,
      builder: (context) => NegotiateDialog(
        offer: offer,
        unit: extractUnit(offer),
      ),
    );
  }

  String extractUnit(Offer item) {
    String input = "N/A";

    var content = bpr?.content?[0];

    if ((content?.mvtItemName ?? "").toLowerCase().contains("reinforcement")) {
      int id = 0;
      if (item.variant1OptionGroupName?.toLowerCase() == "size") {
        id = 1;
      } else if (item.variant2OptionGroupName?.toLowerCase() == "size") {
        id = 2;
      } else if (item.variant3OptionGroupName?.toLowerCase() == "size") {
        id = 3;
      }

      switch (id) {
        case 1:
          input = item.variant1OptionName ?? "";
          break;
        case 2:
          input = item.variant2OptionName ?? "";
          break;
        case 3:
          input = item.variant3OptionName ?? "";
          break;
        default:
          input = "";
      }

      List<String> parts = input.split(' - ');
      if (parts.isNotEmpty) {
        List<String> lastPart = parts.last.split(' ');
        return lastPart.isNotEmpty ? lastPart.last : '';
      }
    } else {
      return content?.optionName ?? "N/A";
    }

    return '';
  }

  void _notifySeller(BuildContext context) {
    // Get the current negotiation offers
    final negotiations = offersList
        .where((e) => e.negotiationHistory?.isNotEmpty == true)
        .toList();

    if (negotiations.isEmpty) {
      alert("No negotiations found to notify about.");
      return;
    }

    // Show confirmation dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            "Notify Seller",
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          content: const Text(
              "This will send a notification to the seller about the negotiation. Do you want to proceed?"),
          actions: <Widget>[
            TextButton(
              child: const Text(
                "No",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text(
                "Yes",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              onPressed: () {
                Navigator.of(context).pop();

                // Find the first negotiation with a negotiation history
                for (var offer in negotiations) {
                  if (offer.id != null) {
                    // Use the notifyBuyer function from OffersCubit
                    context.read<OffersCubit>().notifyBuyer(offer.id!.toInt());
                    return;
                  }
                }

                alert("No valid negotiation ID found to notify about.");
              },
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}

class TabItem extends StatelessWidget {
  final String text;
  final String? prefix;
  final String? suffix;
  final int index;
  final int selectedIndex;
  final Color selectedColor;
  final Color unselectedColor;
  final Color selectedTextColor;
  final Color unselectedTextColor;
  final Function onTap;
  final double width;
  final double? fontSize;

  const TabItem({
    Key? key,
    required this.text,
    this.prefix,
    this.suffix,
    required this.index,
    required this.selectedColor,
    required this.unselectedColor,
    required this.selectedTextColor,
    required this.unselectedTextColor,
    required this.selectedIndex,
    required this.onTap,
    required this.width,
    this.fontSize = 14,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onTap(index);
      },
      child: Container(
        margin: const EdgeInsets.only(top: 1, bottom: 0, left: 0, right: 0),
        child: Material(
          elevation: 2,
          clipBehavior: Clip.hardEdge,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(6),
            topRight: Radius.circular(6),
          ),
          child: Container(
            height: double.infinity,
            width: width,
            decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(6),
                topRight: Radius.circular(6),
              ),
              border: Border.all(color: Colors.black),
              color: index == selectedIndex ? selectedColor : unselectedColor,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (prefix != null) ...[
                  const SizedBox(width: 2),
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.green.shade600,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        prefix!,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 6),
                ],
                Text(
                  text,
                  style: TextStyle(
                    fontSize: fontSize,
                    fontWeight: FontWeight.bold,
                    color: index == selectedIndex
                        ? selectedTextColor
                        : unselectedTextColor,
                  ),
                ),
                if (suffix != null) ...[
                  const SizedBox(width: 6),
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.cyan.shade600,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        suffix!,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 2),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
