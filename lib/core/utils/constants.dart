// rename setAppName --targets ios,android --value "YourAppName"
// rename setBundleId --targets android,ios --value "com.example.bundleId"

////////////////////////////////////////////////////////////////////////////////

/// Demo
// const appTitle = "ConnectOne Demo";
// const iconUrl = "assets/images/demo.png";
// bool isPhoneNumber = false;
// const tableType = "CARDAMOM";
// const baseAppUrl = "https://api-prod.c1io.com";
// const additionalUrl = "https://demo.connectoneclub.com";
// const baseAppCdnUrl = "https://cdn-prod.c1io.com";
// var firebaseBaseUrl = "PROD_AGRI_CARD_TEST_2";
// var androidAppVersion = "53";
// var iosAppVersion = "1";
// const androidBundleId = "com.connectone.demo";
// const versionPrefix = "demo";

// flutter pub global run rename --bundleId com.connectone.demo
// flutter pub global run rename --appname "ConnectOne Demo"
// flutter pub run icons_launcher:create
// flutterfire configure --project=connectone-prod

////////////////////////////////////////////////////////////////////////////////

/// Test
// const appTitle = "ConnectOne Test";
// const iconUrl = "assets/images/demo.png";
// bool isPhoneNumber = false;
// const tableType = "CARDAMOM";
// const baseAppUrl = "https://api-dev.yourservice.tips";
// const additionalUrl = "https://c1-customer-test.yourservice.tips";
// const baseAppCdnUrl = "https://cdn-test.yourservice.tips";
// const firebaseBaseUrl = "AGRI_CARD_VGCP_1";
// var androidAppVersion = "1";
// var iosAppVersion = "0";
// const androidBundleId = "com.connectone.test";
// const versionPrefix = "test";

// flutter pub global run rename --bundleId com.connectone.test
// flutter pub global run rename --appname "ConnectOne Test"
// flutter pub run icons_launcher:create
// flutterfire configure --project=connectone-test

////////////////////////////////////////////////////////////////////////////////

/// ConnectOne in
// const appTitle = "connectone.in";
// const iconUrl = "assets/images/demo.png";
// bool isPhoneNumber = true;
// const tableType = "CARDAMOM";
// const baseAppUrl = "https://api-prod.c1io.com";
// const additionalUrl = "https://connectone.in";
// const baseAppCdnUrl = "https://cdn-prod.c1io.com";
// const firebaseBaseUrl = "PROD_AGRI_CARD_JMBC_7";
// var androidAppVersion = "1";
// var iosAppVersion = "0";
// const androidBundleId = "com.connectone.connectonein";
// const versionPrefix = "in";

// flutter pub global run rename --bundleId com.connectone.connectonein
// flutter pub global run rename --appname "ConnectOne.in"
// flutter pub run icons_launcher:create
// flutterfire configure --project=connectone-prod

////////////////////////////////////////////////////////////////////////////////

/// Circular Farms
// const appTitle = "Circular Farms";
// const iconUrl = "assets/images/demo.png";
// bool isPhoneNumber = false;
// const tableType = "CARDAMOM";
// const baseAppUrl = "https://api-prod.c1io.com";
// const additionalUrl = "https://circularfarms.com.au";
// const baseAppCdnUrl = "https://cdn-prod.c1io.com";
// var firebaseBaseUrl = "PROD_AGRI_CARD_MMGS_8";
// var androidAppVersion = "2";
// var iosAppVersion = "2";
// const androidBundleId = "com.connectone.circularfarms";
// const versionPrefix = "circularfarms";

// flutter pub global run rename --bundleId com.connectone.circularfarms
// flutter pub global run rename --appname "Circular Farms"
// flutter pub run icons_launcher:create
// flutterfire configure --project=connectone-prod

////////////////////////////////////////////////////////////////////////////////
const steelCategoryCode = '300';
////////////////////////////////////////////////////////////////////////////////

/// bai Store
const appTitle = "Bai Store";
const iconUrl = "assets/images/bai.png";
bool isPhoneNumber = true;
const tableType = "CARDAMOM";
// const baseAppUrl = "https://dev-api.cochq.au";
// const additionalUrl = "https://dev-customer.cochq.au";
// const baseAppCdnUrl = "https://dev-api.cochq.au";
const baseAppUrl = "https://prod-api.cochq.au";
const additionalUrl = "https://baistore.cochq.au";
const baseAppCdnUrl = "https://prod-cdn-api.cochq.au";
var firebaseBaseUrl = "PROD_AGRI_CARD_JCPC_3";
var androidAppVersion = "40";
var iosAppVersion = "18";
const androidBundleId = "bai.store.app";
const versionPrefix = "bai";

// flutter pub global run rename --bundleId bai.store.app
// flutter pub global run rename --appname "BAI Store"
// flutter pub run icons_launcher:create
// flutterfire configure --project=connectone-prod

////////////////////////////////////////////////////////////////////////////////

/// eCardamom
// const appTitle = "eCardamom Bidding";
// const iconUrl = "assets/images/ecardamom.png";
// bool isPhoneNumber = true;
// const tableType = "CARDAMOM";
// const baseAppUrl = "https://api-prod.c1io.com";
// const additionalUrl = "https://ecardamom.in";
// const baseAppCdnUrl = "https://cdn-prod.c1io.com";
// var firebaseBaseUrl = "PROD_AGRI_CARD_ECAR_1";
// var androidAppVersion = "44";
// var iosAppVersion = "0";
// const androidBundleId = "com.connectone.ecardamom";
// const versionPrefix = "ecardamom";

// flutter pub global run rename --bundleId com.connectone.ecardamom
// flutter pub global run rename --appname "eCardamom Bidding"
// flutter pub run icons_launcher:create
// flutterfire configure --project=connectone-prod

////////////////////////////////////////////////////////////////////////////////

/// IPSTA Pepper Trade
// const appTitle = "IPSTA Pepper Trade";
// const iconUrl = "assets/images/ipsta.png";
// bool isPhoneNumber = true;
// const tableType = "PEPPER";
// const baseAppUrl = "https://api-prod.c1io.com";
// const additionalUrl = "https://ipsta.connectone.in";
// const baseAppCdnUrl = "https://cdn-prod.c1io.com";
// var firebaseBaseUrl = "PROD_AGRI_CARD_ADHF_9";
// var androidAppVersion = "22";
// var iosAppVersion = "24";
// const androidBundleId = "ipsta.pepper.etrade";
// const versionPrefix = "ipsta";

// flutter pub global run rename --bundleId ipsta.pepper.etrade
// flutter pub global run rename --appname "IPSTA Pepper Trade"
// flutter pub run icons_launcher:create
// flutterfire configure --project=connectone-prod

////////////////////////////////////////////////////////////////////////////////

/// Spices Mandi
// const appTitle = "Spices Mandi";
// const iconUrl = "assets/images/spices.png";
// bool isPhoneNumber = true;
// const tableType = "PEPPER";
// const baseAppUrl = "https://api-prod.c1io.com";
// const additionalUrl = "https://spicesmandi.com";
// const baseAppCdnUrl = "https://cdn-prod.c1io.com";
// var firebaseBaseUrl = "PROD_AGRI_CARD_JCPC_3";
// var androidAppVersion = "6";
// var iosAppVersion = "4";
// const androidBundleId = "spices.mandi.app";
// const versionPrefix = "spices";

// flutter pub global run rename --bundleId spices.mandi.app
// flutter pub global run rename --appname "Spices Mandi"
// flutter pub run icons_launcher:create
// flutterfire configure --project=connectone-prod

/// build variables
// const appTitle = "ConnectOne Demo";
// const iconUrl = "assets/images/demo.png";
// bool isPhoneNumber = false;

////////////////////////////////////////////////////////////////////////////////

// others
const dataRoomId = "999";
const customerRole = "customerRole";
const customerId = "customerId";
const cognitoToken = "cognitoToken";
const loggedIn = "loggedIn";

/// strings
const userName = "User";
const seller = "Seller";
const grade = "Grade";
const dateTime = "Date & Time";
const location = "Location";
const qty = "Quantity";
const noBid = "No of Bid";
const highest = "Highest Bid";
const totalStocks = "Total Stocks";
const totalQty = "Quantity";
const totalAmnt = "Total Amount";
const sEmail = "Email";
const sContinue = "Continue";
const sVerifyOtp = "Save";
const sPassword = "Password";
const sRememberMe = "Remember me";
const sForgotPassword = "Forgot password?";
const sLogin = "Login";
const sNewToConnectOne = "New to ConnectOne";
const sRegister = " Register";
const sEnterAValidEmail = "Enter a valid Email.";
const sCodeIsSendTo = "Code is Send To";
const sSomethingWentWrong = "Something went wrong.";
const sEnterAValidCode = "Enter a valid code.";
const sPasswordChangedSuccessfully = "Password changed successfully.";
const sPasswordChangeFailed = "Password change failed.";
const sEnterAValidUsername = "Enter a valid username.";
const sEnterAValidPassword = "Enter a valid password.";
const sInvalidUsernameOrPassword = "Incorrect username or password.";
const sLoginFailed = "Incorrect username or password.";
const sPasswordNotMatching = "Passwords not matching.";

/// button Text
const paymentButton = "Payment";
const feedbackButton = "Feedback";
const openButton = "Open";

/// settings
const toneF = "toneF";
const vibF = "vibF";
