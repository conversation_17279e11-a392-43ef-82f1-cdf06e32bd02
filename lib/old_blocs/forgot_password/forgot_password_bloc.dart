import 'package:amazon_cognito_identity_dart_2/cognito.dart';
import 'package:connectone/old_blocs/forgot_password/forgot_password_state.dart';
import 'package:connectone/old_screens/login_screen.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

import '../../core/network/network_controller.dart';
import '../../core/utils/constants.dart';

class ForgotPasswordBloc extends Cubit<ForgotPasswordState> {
  ForgotPasswordBloc(initialState) : super(initialState);
  final _networkController = NetworkController();
  FirebaseAuth auth = FirebaseAuth.instance;
  String? vCognitoToken;

  willPopClick() {
    var newState = state.copyWith(
        confirmationCode: !state.confirmationCode,
        emailEditable: !state.emailEditable);
    emit(newState);
  }

  forgotPassword(String username) async {
    if (username.isEmpty) {
      alert(sEnterAValidEmail);
      return;
    }
    if (!username.contains("@")) {
      username = "+91$username";
    }
    var newState = state.copyWith(isLoading: true);
    emit(newState);
    final userPool = CognitoUserPool(
      _networkController.organisationData?.userPoolId ?? "",
      _networkController.organisationData?.appClientWeb ?? "",
    );
    final cognitoUser = CognitoUser(username, userPool);
    try {
      var data = await cognitoUser.forgotPassword();
      alert("$sCodeIsSendTo $username");
      safePrint(data);
      var newState = state.copyWith(
          isLoading: false, confirmationCode: true, emailEditable: false);
      emit(newState);
    } catch (e) {
      safePrint(e);
      var newState = state.copyWith(isLoading: false);
      emit(newState);
      if (e is CognitoClientException) {
        alert(e.message);
      } else {
        alert(sSomethingWentWrong);
      }
    }
  }

  Future<bool> confirmPassword(String username, String code, String newPassword,
      String confirmPassword) async {
    final userPool = CognitoUserPool(
      _networkController.organisationData?.userPoolId ?? "",
      _networkController.organisationData?.appClientWeb ?? "",
    );
    if (isPhoneNumber) {
      username = "+91$username";
    }
    safePrint("pppp $username $code $newPassword $confirmPassword");
    final cognitoUser = CognitoUser(username, userPool);
    try {
      if (code.isEmpty) {
        alert(sEnterAValidCode);
        return false;
      }
      if (newPassword.isEmpty || newPassword != confirmPassword) {
        alert("Please enter correct passwords!");
        return false;
      }
      var newState = state.copyWith(isLoading: true);
      emit(newState);
      var passwordConfirmed =
          await cognitoUser.confirmPassword(code, newPassword);

      var newState1 = state.copyWith(isLoading: false);
      emit(newState1);
      alert(sPasswordChangedSuccessfully);
      Get.off(() => const LoginScreen());
      return passwordConfirmed;
    } catch (e) {
      safePrint("qqqq $e");
      var newState = state.copyWith(isLoading: false);
      emit(newState);
      if (e is CognitoClientException) {
        alert(e.message);
      } else {
        alert(sPasswordChangeFailed);
      }
      return false;
    }
  }
}
