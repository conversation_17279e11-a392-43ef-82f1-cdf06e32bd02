import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/history_res.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:equatable/equatable.dart';

part 'history_state.dart';

class HistoryCubit extends Cubit<HistoryState> {
  HistoryCubit() : super(HistoryInitial());
  var api = NetworkController();
  Future<void> loadHistory(String prchOrdrId) async {
    emit(HistoryLoading());
    try {
      var res = await api.getHistory(prchOrdrId);
      emit(HistoryLoaded(historyRes: res));
    } catch (e) {
      emit(HistoryError());
    }
  }
}
