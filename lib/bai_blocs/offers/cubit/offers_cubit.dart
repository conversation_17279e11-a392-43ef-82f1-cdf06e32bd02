import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/accept_negotiation_req.dart';
import 'package:connectone/bai_models/approve_po_req.dart';
import 'package:connectone/bai_models/seller_offers_res.dart';
import 'package:connectone/bai_models/negotiation_req.dart' as nr;
import 'package:connectone/bai_models/offers_filter_res.dart';
import 'package:connectone/bai_models/offers_req.dart' as request;
import 'package:connectone/bai_models/offers_res.dart';
import 'package:connectone/bai_models/view_offer_req.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_models/status_list_model.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

import '../../../bai_models/not_interested_req.dart';
import '../../../bai_models/summary_res.dart';

part 'offers_state.dart';

class OffersCubit extends Cubit<OffersState> {
  OffersCubit() : super(OffersInitial());

  var api = NetworkController();

  postSellerOffers(
    List<request.OffersReq> offers,
    String productId,
    String gst,
  ) async {
    emit(BuyerOffersLoading());
    try {
      await api.postGst(productId, gst);
      for (var req in offers) {
        req.gst = double.tryParse(gst);
        req.medias ??= [];
        if (kIsWeb) {
          var images = req.bytes;
          if (images != null && images.isNotEmpty) {
            req.medias ??= [];
            for (var image in images) {
              var res = await api.uploadMediaWeb(image);
              req.medias?.add(request.Media(url: res.fileUrl));
            }
          }
        } else {
          var images = req.paths;
          if (images != null && images.isNotEmpty) {
            req.medias ??= [];
            for (var image in images) {
              var res = await api.uploadMedia(image);
              req.medias?.add(request.Media(url: res.fileUrl));
            }
          }
        }

        var postRes = await api.postOffers(req);
      }
      emit(BuyerOffersSuccess());
    } catch (e) {
      emit(BuyerOffersFailed(message: e.toString()));
    }
  }

  loadBuyerOffers(
    String prchId,
    ViewOfferReq req,
    int ordrGrpId,
    String categoryId, {
    bool enablePoSummary = false,
    required DateTime date,
    int? prchOrdrSplitId,
    required bool steelMr,
  }) async {
    emit(SellerOffersLoading());
    try {
      var res = await api.getBuyerOffers(prchId, req);

      var isEmpty = (res.offers?.length ?? 0) == 0;

      List<SummaryResponse> sum = [];

      if (steelMr) {
        sum = await api.getQuoteSummarySteel(
          ordrGrpId,
          responseType: isEmpty ? "MRSM" : "QTSM",
          categoryId: categoryId,
          prchOrdrSplitId: int.tryParse(categoryId.toEmptyIfZero()),
          date: date,
        );
      } else {
        sum = await api.getQuoteSummary(
          ordrGrpId,
          responseType: isEmpty ? "MRSM" : "QTSM",
          categoryId: categoryId,
          prchOrdrSplitId: int.tryParse(categoryId.toEmptyIfZero()),
          enablePoSummary: enablePoSummary,
          date: date,
        );
      }

      var filters = await api.getOffersFilter(prchId);

      StatusListModel? nextStatus;

      if (res.mrStatus != null) {
        nextStatus = await api.getStatusDropdown(
          res.mrStatus ?? "",
          categoryId: categoryId,
          prchOrdrSplitId: prchOrdrSplitId,
          deliveryDate: date,
        );
      }

      if (nextStatus?.data != null) {
        // var designation = getDesignation().toLowerCase();
        var roleCode = getRoleLevel();
        var isAdmin = roleCode == 1;
        var isManagerOrAsstManager = roleCode == 5 || roleCode == 10;

        var excludedStatuses = ['NEWW', 'REJD', 'EDIT', 'SPLT', 'ODCF', 'ODDP'];
        var conditionalStatuses = [
          'QUOT',
          'NEGO',
          'VASS',
          'PLCD',
          'PYIN',
          'CLSD'
        ];

        nextStatus?.data = nextStatus.data?.where((item) {
          var statusCode = item.statusCode;

          if (excludedStatuses.contains(statusCode)) {
            return false;
          }

          if (conditionalStatuses.contains(res.mrStatus)) {
            return isAdmin || isManagerOrAsstManager;
          }

          return true;
        }).toList();
      }

      var offers = res.offers;

      offers?.sort((a, b) =>
          (a.statusCd == "APPR" ? -1 : 1) - (b.statusCd == "APPR" ? -1 : 1));

      res.offers = offers;

      emit(SellerOffersLoaded(
        summary: sum,
        offers: res,
        filter: filters,
        nextStatus: nextStatus,
      ));
    } catch (e) {
      emit(SellerOffersFailed());
    }
  }

  loadSellerOffers(
    String prchId,
    ViewOfferReq req,
    int ordrGrpId,
    String categoryId, {
    bool enablePoSummary = false,
    int? prchOrdrSplitId,
    required DateTime date,
    required bool steelMr,
  }) async {
    emit(BuyerOffersLoading());
    try {
      var res = await api.getOffersSeller(prchId, req);

      var isAlreadySubmitted = res.alreadySubmittedQuote?.isNotEmpty ?? false;

      List<SummaryResponse> sum = [];

      if (steelMr) {
        sum = await api.getQuoteSummarySteel(
          ordrGrpId,
          responseType: isAlreadySubmitted ? "QTSM" : "MRSM",
          categoryId: categoryId,
          prchOrdrSplitId: int.tryParse(categoryId.toEmptyIfZero()),
          vendorId: getVendorId(),
          date: date,
        );
      } else {
        sum = await api.getQuoteSummary(
          ordrGrpId,
          responseType: isAlreadySubmitted ? "QTSM" : "MRSM",
          categoryId: categoryId,
          prchOrdrSplitId: int.tryParse(categoryId.toEmptyIfZero()),
          enablePoSummary: enablePoSummary,
          vendorId: getVendorId(),
          date: date,
        );
      }

      var filters = await api.getOffersFilter(prchId);

      StatusListModel? nextStatus;

      if (res.mrStatus != null) {
        nextStatus = await api.getStatusDropdown(
          res.mrStatus ?? "",
          categoryId: categoryId,
          deliveryDate: date,
        );
      }

      nextStatus?.data = nextStatus.data?.where((item) {
        return item.statusCode == 'ODCF' || item.statusCode == 'ODDP';
      }).toList();

      emit(BuyerOffersLoaded(
        offers: res,
        filter: filters,
        summary: sum,
        nextStatus: nextStatus,
      ));
    } catch (e) {
      emit(BuyerOffersFailed(message: e.toString()));
    }
  }

  loadQuoteSummary(
    int ordrGrpId,
    String categoryId, {
    bool enablePoSummary = false,
    required DateTime date,
    int? prchOrdrSplitId,
    bool steelMr = false,
  }) async {
    emit(QuoteSummaryLoading());
    try {
      List<SummaryResponse> sum = [];
      if (steelMr) {
        sum = await api.getQuoteSummarySteel(
          ordrGrpId,
          responseType: "MRSM",
          categoryId: categoryId,
          prchOrdrSplitId: int.tryParse(categoryId),
          date: date,
        );
      } else {
        sum = await api.getQuoteSummary(
          ordrGrpId,
          responseType: "MRSM",
          categoryId: categoryId,
          prchOrdrSplitId: int.tryParse(categoryId.toEmptyIfZero()),
          enablePoSummary: enablePoSummary,
          date: date,
        );
      }
      emit(QuoteSummaryLoaded(summary: sum));
    } catch (e) {
      emit(QuoteSummaryError(message: e.toString()));
    }
  }

  loadQuotesOnly(
    int prchOrdrId,
    ViewOfferReq req,
  ) async {
    emit(QuotesOnlyLoading());
    try {
      var bpr = await api.getBaiProductListforNotification(
        queryString: prchOrdrId.toString(),
        page: 0,
        code: '',
        sortBy: '',
      );
      var res = await api.getBuyerOffers(
        bpr.content?[0].prchOrdrId?.toString() ?? "0",
        req,
      );
      emit(QuotesOnlyLoaded(buyerOffers: res));
    } catch (e) {
      emit(QuotesOnlyError(message: e.toString()));
    }
  }

  Future<void> acceptOffer(ApprovePoReq req) async {
    emit(BuyerOffersLoading());
    try {
      var res = await api.approvePo(req);
      var items = res.data;

      if (items == null || items.isEmpty) {
        emit(const AcceptNegotiateFailed(message: "No items found."));
        return;
      }

      var ids = items.map((e) => e.id).toList();
      var offerPrices = items.map((e) => e.offerPrice).toList();
      var sellerNames = items.map((e) => e.sellerVendorName).toList();

      String message;

      if (res.status == 200) {
        String idMessage = ids.length == 1
            ? "Purchase Order ID: ${ids.first}"
            : "Purchase Order IDs: ${ids.join(', ')}";
        String priceMessage = offerPrices.length == 1
            ? "Quoted Price: ${offerPrices.first?.toStringAsFixed(2)}"
            : "Quoted Prices: ${offerPrices.map((price) => price?.toStringAsFixed(2)).join(', ')}";
        String sellerMessage = sellerNames.length == 1
            ? "Vendor ${sellerNames.first} is assigned for the Material Request."
            : "Vendors ${sellerNames.join(', ')} are assigned for the Material Request.";

        message = "$sellerMessage\n\n$idMessage\n$priceMessage";
      } else {
        message = res.statusDescription ?? "Failed to accept the quote";
      }

      emit(BuyerOffersAccepted(message: message));
    } catch (e) {
      emit(AcceptNegotiateFailed(
          message: "Failed to accept the quote: ${e.toString()}"));
    }
  }

  Future<void> negotiateOffer(
    String offerId,
    double price, {
    String? notes,
    String? audio,
    required List<String> paths,
    required List<Uint8List> bytes,
    required double quantity,
  }) async {
    emit(BuyerOffersLoading());
    try {
      List<nr.MediaRequest> mediaRequests = [];
      var url = "";
      if (kIsWeb) {
        for (var file in bytes) {
          var res = await api.uploadMediaWeb(file);
          url = res.fileUrl ?? "";
          mediaRequests.add(nr.MediaRequest(url: url));
        }
      } else {
        for (var file in paths) {
          var res = await api.uploadMedia(file);
          url = res.fileUrl ?? "";
          mediaRequests.add(nr.MediaRequest(url: url));
        }
      }

      var req = nr.NegotiationReq(
        negotiatedPrice: price.toInt(),
        negotiationBuyrComment: isBuyer() ? notes : null,
        negotiationMbmrComment: isBuyer() ? null : notes,
        mediaRequests: mediaRequests.isNotEmpty ? mediaRequests : null,
        quantity: quantity,
      );
      await api.postNegotiation(offerId, req);
      // emit(const RefreshBuyerPage());
      emit(const BuyerOffersNegotiated(
          message: "Quotes Negotiated Successfully"));
    } catch (e) {
      emit(AcceptNegotiateFailed(message: e.toString()));
    }
  }

  Future<void> acceptNegotiation(
    String offerId,
    AcceptNegotiationReq req,
  ) async {
    emit(BuyerOffersLoading());
    try {
      await api.acceptNegotiation(offerId, req);
      emit(const NegotiationAccepted(
          message: "Negotiation Accepted Successfully"));
    } catch (e) {
      emit(AcceptNegotiateFailed(message: e.toString()));
    }
  }

  Future<void> sendEmail(String ordrGrpId, DateTime date) async {
    try {
      await api.sendEmail(ordrGrpId, date);
      alert("Quote was emailed successfully.");
    } catch (e) {
      safePrint(e);
      alert("Failed to send email: ${e.toString()}");
    }
  }

  Future<void> discardOffer({
    required String prchOrdrOffrId,
    required bool isDiscard,
  }) async {
    try {
      await api.discardOffer(
        prchOrdrOffrId: prchOrdrOffrId,
        isDiscard: isDiscard,
      );
      emit(DiscardSuccess(isDiscard
          ? "Quote was discarded successfully."
          : "Discarded quote was reactivated successfully."));
    } catch (e) {
      safePrint(e);
      alert(e.toString());
    }
  }

  void refreshBuyerPage() {
    emit(const RefreshBuyerPage());
  }

  void refreshSellerPage() {
    emit(const RefreshSellerPage());
  }

  Future<void> notInterested(NotInterestedReq req) async {
    try {
      await api.notInterested(req);
      emit(const NotInterestedSuccess());
    } catch (e) {
      safePrint(e);
    }
  }

  Future<void> notifyBuyer(int prchOrdrOffrId) async {
    var previousState = state;
    emit(BuyerOffersLoading());
    try {
      await api.notify(prchOrdrOffrId);
      emit(NotifyBuyerSuccess(
          "${isBuyer() ? "Seller" : "Buyer"} has been notified successfully."));
      emit(previousState);
    } catch (e) {
      safePrint(e);
      emit(NotifyBuyerFailed(e.toString()));
      emit(previousState);
    }
  }
}
