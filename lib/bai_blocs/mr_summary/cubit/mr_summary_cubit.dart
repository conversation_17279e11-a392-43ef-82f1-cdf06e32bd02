import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../bai_models/delivery_dates_res.dart';
import '../../../core/network/network_controller.dart';

part 'mr_summary_state.dart';

class MrSummaryCubit extends Cubit<MrSummaryState> {
  MrSummaryCubit() : super(MrSummaryInitial());

  Future<void> getDeliveryDates(String orderGroupId) async {
    emit(MrSummaryLoading());
    try {
      var res = await NetworkController()
          .getDeliveryDates(orderGroupId: orderGroupId);
      var sortedDeliveryDates = res.data?.deliveryDates ?? [];
      sortedDeliveryDates.sort((a, b) => (b.deliveryDate ?? DateTime(0))
          .compareTo(a.deliveryDate ?? DateTime(0)));
      res.data?.deliveryDates = sortedDeliveryDates;
      emit(MrSummaryLoaded(res));
    } catch (e) {
      emit(MrSummaryError(e.toString()));
    }
  }
}
