import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/bai_models/offers_filter_res.dart';
import 'package:connectone/bai_models/offers_req.dart' as request;
import 'package:connectone/bai_models/offers_res.dart';
import 'package:connectone/bai_models/view_offer_req.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:equatable/equatable.dart';

import '../../../bai_models/seller_offers_res.dart';

part 'quotes_only_state.dart';

class QuotesOnlyCubit extends Cubit<QuotesOnlyState> {
  QuotesOnlyCubit() : super(QuotesOnlyInitial());

  var api = NetworkController();

  loadQuotesBuyer(int prchOrdrId, ViewOfferReq req,
      {String? brand = ""}) async {
    emit(BuyerQuotesLoading());
    try {
      var bpr = await api.getBaiProductListforNotification(
        queryString: prchOrdrId.toString(),
        page: 0,
        code: '',
        sortBy: '',
      );
      var filters = await api.getOffersFilter(prchOrdrId.toString());
      var res = await api.getBuyerOffers(
        bpr.content?[0].prchOrdrId?.toString() ?? "0",
        req,
        brandName: brand,
      );
      emit(BuyerQuotesLoaded(
        buyerOffers: res,
        bpr: bpr,
        filter: filters,
      ));
    } catch (e) {
      emit(BuyerQuotesError(message: e.toString()));
    }
  }

  loadQuotesSeller(
    String prchId,
    ViewOfferReq req,
    int ordrGrpId,
  ) async {
    emit(SellerQuotesLoading());
    try {
      var res = await api.getOffersSeller(prchId, req);
      var bpr = await api.getBaiProductListforNotification(
        queryString: prchId.toString(),
        page: 0,
        code: '',
        sortBy: '',
      );
      var filters = await api.getOffersFilter(prchId);
      emit(SellerQuotesLoaded(
        offers: res,
        itemsContent: bpr,
        filter: filters,
      ));
    } catch (e) {
      emit(SellerQuotesFailed(message: e.toString()));
    }
  }

  postSellerOffers(
    List<request.OffersReq> offers,
    String productId,
    String gst,
    bool isEnableGst,
  ) async {
    emit(SellerQuotesLoading());
    try {
      if (isEnableGst) {
        await api.postGst(productId, gst);
      }

      for (var req in offers) {
        var images = req.paths;
        req.gst = double.tryParse(gst);
        if (images != null && images.isNotEmpty) {
          req.medias ??= [];

          for (var image in images) {
            var res = await api.uploadMedia(image);
            req.medias?.add(request.Media(url: res.fileUrl));
          }
        }
        var postRes = await api.postOffers(req);
      }
      emit(SellerQuotesSuccess());
    } catch (e) {
      emit(SellerQuotesFailed(message: e.toString()));
    }
  }
}
